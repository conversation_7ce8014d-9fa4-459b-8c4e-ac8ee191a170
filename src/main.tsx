/* eslint-disable no-underscore-dangle */
// eslint-disable-next-line import/no-extraneous-dependencies
import '@echronos/ech-micro/runtime';
import ReactDOM from 'react-dom';
import { BrowserRouter } from 'react-router-dom';
import { onReady, setHttpConfig } from '@echronos/core';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import App from './app';
import './i18n';
import './index.less';

setHttpConfig({
  baseURL: import.meta.env.BIZ_API_URL || '/',
  timeout: +(import.meta.env.BIZ_API_TIMEOUT || 10000),
  headers: {
    common: {
      satype: import.meta.env.BIZ_API_SA_TYPE || '',
    },
  },
});

// @ts-expect-error todo
window.BASE_API_URL = import.meta.env.BIZ_API_URL;

// 👇 将渲染操作放入 mount 函数 -- 必填
export function mount() {
  onReady(() => {
    ReactDOM.render(
      <BrowserRouter basename="/">
        <ConfigProvider locale={zhCN}>
          <App />
        </ConfigProvider>
      </BrowserRouter>,
      document.querySelector('#micro-root')!
    );
  });
}

// 👇 将卸载操作放入 unmount 函数 -- 必填
export function unmount() {
  ReactDOM.unmountComponentAtNode(document.querySelector('#micro-root')!);
}

mount();
