import { useParams } from 'react-router-dom';
import { useMemo } from 'react';
import size from '../../../blocks/size';
import * as blocks from '../../../blocks';
import BlockContainer from '../../../blocks/block-container';
import './index.less';

function Home() {
  const { blockName } = useParams();
  const Ele = blocks[blockName];
  const sizeArr = useMemo(() => {
    if (size[blockName]) {
      return size[blockName];
    }
    return [2, 2];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <div className="home">
      <h2>大网格</h2>
      <div
        className="grid"
        style={{
          width: `${140 * sizeArr[0]}px`,
          height: `${140 * sizeArr[1]}px`,
        }}
      >
        <BlockContainer id={blockName}>
          <Ele />
        </BlockContainer>
      </div>
      <h2>小网格</h2>
      <div
        className="grid"
        style={{
          width: `${87 * sizeArr[0]}px`,
          height: `${96 * sizeArr[1]}px`,
        }}
      >
        <BlockContainer id={blockName}>
          <Ele />
        </BlockContainer>
      </div>
    </div>
  );
}

export default Home;
