import * as blocks from '../../../blocks';
import { useNavigate } from 'react-router-dom';
import './index.less';

const arr = Object.keys(blocks);
function Introduce() {
  const navigate = useNavigate();
  return (
    <div>
      <h1>请在路由上加上你想访问的组件名称，例如（http://localhost:9000/banner）</h1>
      <div>
        下面是现有的组件列表
        <span
          style={{
            color: 'red',
          }}
        >
          (某些组件需要登录，先在9000端口运行echOS main项目登录后再运行此项目)
        </span>
      </div>
      <ul>
        {arr.map((item) => {
          return (
            <li
              key={item}
              className="menu"
              onClick={() => {
                navigate('/' + item);
              }}
            >
              {item}
            </li>
          );
        })}
      </ul>
    </div>
  );
}

export default Introduce;
