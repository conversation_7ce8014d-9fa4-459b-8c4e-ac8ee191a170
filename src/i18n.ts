import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import zh from './locales/zh.json';
import ms from './locales/ms.json';
import en from './locales/en.json';

const resources = {
  zh: {
    translation: zh,
  },
  ms: {
    translation: ms,
  },
  en: {
    translation: en,
  },
};

const i18nInit = (l: string) => {
  i18n.use(initReactI18next).init({
    resources,
    fallbackLng: l,
    interpolation: {
      escapeValue: false,
    },
    detection: {
      order: ['path', 'htmlTag', 'cookie', 'navigator'],
      caches: ['localStorage'],
    },
  });
};

const lang = localStorage.getItem('lang') || 'zh';

// 微应用情况下直接初始化
if (window.microApp?.getData()) {
  i18nInit(lang);
}

export default i18n;

export { i18nInit };
