import { createHttpRequest, PaginationResponse } from '@echronos/core';

export interface MemberListType {
  dataId: number; // 数据ID
  dataName: string; // 数据名称
}

function getSelectCondition(quarter: number | number[]): PaginationResponse<MemberListType> {
  return createHttpRequest('ech-crm-new')('/v2/home/<USER>/getSelectCondition', {
    method: 'GET',
    params: {
      conditionType: 2, // 选择成员
      quarter,
    },
    autoToast: false,
  });
}

export default getSelectCondition;
