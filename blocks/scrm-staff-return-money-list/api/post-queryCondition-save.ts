import { createHttpRequest } from '@echronos/core';

/**
 * 销售团队回款数据分析,卡片查询条件保存
 */

// quarterList：销售团队回款数据分析--季度多选条件【1,2,3,4,]【第一季度-1，第二季度-2，第三季度-3，第四季度-4】

function postQueryConditionSave(quarterList: number[], userList: number[], blockId: string) {
  return createHttpRequest('ech-crm-new')('/v2/home/<USER>/queryCondition/save', {
    method: 'POST',
    data: {
      type: 4,
      quarterList,
      userList,
      cardUuid: blockId,
    },
    autoToast: false,
  });
}

export default postQueryConditionSave;
