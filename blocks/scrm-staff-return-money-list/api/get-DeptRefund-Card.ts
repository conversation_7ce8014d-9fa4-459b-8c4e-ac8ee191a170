import { createHttpRequest } from '@echronos/core';

/**
 * 获取销售团队回款数据分析卡片
 */

export interface MemberInfoType {
  memberId: number; // 成员ID
  memberName: string; // 成员名称
  userId: number; // 用户Id
  companyId: number; // 公司id
  avatar: string; // 成员头像
}

interface MonthReturnedMoneyMapType {
  [key: string]: {
    money: string; // 金额【单位：万元】
    goalRate?: string; // 占目标倍率
    holidayMoney?: string; // 历史对比金额【单位：万元】
    changeMoney: string; // 变化金额【单位：万元】
    changeType: number; // 变化类型 0-没变化，1-新增，2-减少
  };
}

interface opportunityType {
  opportunityId: number; // 商机ID
  opportunityName: string; // 商机名称
  phaseName: string; // 当前商机阶段名称
  monthReturnedMoneyMap: MonthReturnedMoneyMapType; // 月预计回款金额 month -> money
}

export interface MemberRefundCardType {
  memberList: MemberInfoType[]; // 员工信息
  monthList: string[]; // 统计月列表
  monthReturnedMoneyMap: MonthReturnedMoneyMapType; // 月预计回款金额合计 month -> money
  opportunityCardList: opportunityType[]; // 商机金额列表
  deptStatisticsQuarter: number[]; // 季度多选条件
  userStatisticsList: number[]; // 员工UserId多选条件
}

function getMemberRefundCard(blockId: string): Promise<MemberRefundCardType> {
  return createHttpRequest('ech-crm-new')('/v2/home/<USER>/getMemberRefundCard', {
    method: 'GET',
    params: {
      cardUuid: blockId,
    },
    autoToast: false,
  });
}

export default getMemberRefundCard;
