import React, { useEffect, useRef, useState } from 'react';
import classNames from 'classnames';
import { Spin, Tooltip } from 'antd';
import SelectionCondition, { getCurrentQuarter } from '../components/selection-condition';
import useElSize from '../utils/hooks/use-el-size';
import { IEchBlockCard } from '../interface';
import useLang from '../utils/hooks/use-lang';
import {
  getMemberRefundCard,
  postQueryConditionSave,
  MemberRefundCardType,
  MemberInfoType,
} from './api';
import CardContext from '../components/card-context';
import zh from './locales/zh.json';
import en from './locales/en.json';
import './index.less';

function ScrmStaffReturnMoneyList({ navigate, blockId }: IEchBlockCard) {
  const t = useLang({
    zh,
    en,
  });
  const [loading, setLoading] = useState(false); // 数据加载

  const [hover, setHover] = useState(false); // 编辑按钮显隐
  const [editBtnShow, setEditBtnShow] = useState(false); // 编辑按钮
  const [editBoxShow, setEditBoxShow] = useState(false); // 编辑盒子
  const [maskShow, setMaskShow] = useState(false); // 蒙版显隐

  const [quarter, setQuarter] = useState(getCurrentQuarter([])); // 季度

  const [memberId, setMemberId] = useState<number[]>([]); // 成员id
  const [memberInfoList, setMemberInfoList] = useState<MemberInfoType[] | null>(null); // 成员信息列表

  // 季度员工回款数据
  const [memberRefundData, setMemberRefundData] = useState<MemberRefundCardType | null>(null);

  // 获取当前卡片的DOM元素
  const echBlockScrmStaffReturnMoneyListRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockScrmStaffReturnMoneyListRef, 324);

  // 鼠标移入移出——编辑按钮显隐
  const handleMouseEnter = () => {
    if (maskShow) return;
    setHover(true);
    setEditBtnShow(true);
  };
  const handleMouseLeave = () => {
    if (maskShow) return;
    setHover(false);
    setEditBtnShow(false);
  };

  // 获取卡片数据
  const getMemberStatisticsData = () => {
    getMemberRefundCard(blockId?.toString() || '')
      .then((res: MemberRefundCardType) => {
        // 如果没有保存卡片查询条件，则自动进入选择查询条件
        if (!res.deptStatisticsQuarter.length || !res.userStatisticsList.length) {
          setMaskShow(true);
          return;
        }
        // 保存当前查询条件
        setMemberRefundData(res);
        setMemberInfoList(res.memberList);
        setQuarter(res.deptStatisticsQuarter);
        setMemberId(res.userStatisticsList);
        setEditBoxShow(false);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 完成
  const handleSuccess = (quarters: number[], deptIds: number[]) => {
    setLoading(true);
    postQueryConditionSave(quarters, deptIds, blockId?.toString() || '').finally(() =>
      getMemberStatisticsData()
    );
  };

  // 首次渲染时获取卡片数据
  useEffect(() => {
    getMemberStatisticsData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 跳转进入数据分析页面
  const onJumpPage = () => {
    if (maskShow) return;
    if (navigate) {
      navigate(`/scrm/client/report-forms?quarter=${memberRefundData?.deptStatisticsQuarter[0]}`);
    }
  };

  return (
    <CardContext loading={loading} isPermission spinWrapperClassName="spinWrap">
      <div
        ref={echBlockScrmStaffReturnMoneyListRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className={classNames('ech-block-scrm-staff-return-money-list', { small: isSmall })}
        role="button"
        tabIndex={0}
        onClick={onJumpPage}
      >
        <div className={classNames('container', { dis: editBoxShow })}>
          {!editBoxShow ? (
            <>
              {/* 员工logo+员工名称 */}
              <div className="headBox">
                <img
                  src={
                    memberInfoList?.length
                      ? memberInfoList[0].avatar
                      : `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1723186307558261.png`
                  }
                  alt=""
                  className="imgBox"
                />

                {memberInfoList && memberInfoList[0].memberName.length > 12 ? (
                  <Tooltip title={memberInfoList[0]?.memberName}>
                    {memberInfoList[0]?.memberName || '--'}
                  </Tooltip>
                ) : (
                  <span className="headName">
                    {(memberInfoList && memberInfoList[0]?.memberName) || '--'}
                  </span>
                )}
              </div>
              {/* 回款列表 */}
              <div className="table">
                <div className="tableHead">
                  <div className="chanceAndPhase">
                    <div className="chanceHeader">{t('desk_scrmStaffReturn_opportunity')}</div>
                    <div className="phaseHeader">{t('desk_scrmStaffReturn_phase')}</div>
                  </div>
                  <div className="refundList">
                    {memberRefundData?.monthList.map((item) => (
                      <div className="refundHeader" key={item}>
                        {t('desk_scrmStaffReturn_monthExpected', { month: item })}
                      </div>
                    ))}
                  </div>
                </div>
                <Spin spinning={loading} delay={500}>
                  <div className="tableBody" onWheel={(e) => e.stopPropagation()}>
                    {memberRefundData?.opportunityCardList.length ? (
                      memberRefundData?.opportunityCardList.map((item) => (
                        <div className="tableROw" key={item.opportunityId}>
                          <div className="chanceAndPhase">
                            <div className="chanceItem">{item.opportunityName || '--'}</div>
                            <div className="phaseItem">{item.phaseName || '--'}</div>
                          </div>
                          <div className="refundList">
                            {memberRefundData?.monthList.map((key) => (
                              <div className="refundItem" key={key}>
                                {t('desk_scrmStaffReturn_amount', {
                                  amount: item?.monthReturnedMoneyMap[key] || '--',
                                })}
                              </div>
                            ))}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="nullData">{t('desk_scrmStaffReturn_noData')}</div>
                    )}
                  </div>
                </Spin>
                {/* 移入滚动的时候占位 */}
                {!loading && (editBtnShow || hover) ? <div style={{ height: '26px' }} /> : null}
              </div>

              {/* 编辑按钮 */}
              {editBtnShow && (
                <div
                  className="editBtn"
                  role="button"
                  tabIndex={0}
                  onClick={(e) => {
                    e.stopPropagation();
                    setEditBoxShow(true);
                  }}
                >
                  {t('desk_scrmStaffReturn_edit')}
                </div>
              )}
            </>
          ) : null}
          {!loading && (editBoxShow || hover) ? (
            <SelectionCondition
              size={echBlockScrmStaffReturnMoneyListRef.current?.offsetWidth}
              isSmall={isSmall}
              className="editBox"
              memberName={t('desk_scrmStaffReturn_staff')}
              radioQuarter
              visible={editBoxShow}
              quartered={quarter}
              memberType={2}
              radioMember
              memberIded={memberId}
              onSuccess={(quarters, deptIds) => {
                handleSuccess(quarters, deptIds || []);
              }}
            />
          ) : null}
          {/* 蒙版 */}
          {maskShow ? (
            <div className="mask">
              <div className="maskTitle">{t('desk_scrmStaffReturn_maskTitle')}</div>
              <div
                className="maskBtn"
                role="button"
                tabIndex={0}
                onClick={(e) => {
                  e.stopPropagation();
                  setEditBoxShow(true);
                  setMaskShow(false);
                }}
              >
                {t('desk_scrmStaffReturn_setConditions')}
              </div>
            </div>
          ) : null}
        </div>
      </div>
    </CardContext>
  );
}

export default ScrmStaffReturnMoneyList;
