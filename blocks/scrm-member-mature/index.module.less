.echBlockScrmMemberMature {
  font-size: 14px;
  display: flex;
  width: 100%;
  height: 100%;
  padding: 12px;
  position: relative;
  font-family: '苹方-简', sans-serif;
  flex-direction: column;
  background: rgb(255 255 255 / 70%);
  backdrop-filter: blur(25px);
  cursor: pointer;

  .headBox {
    color: #040919;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    width: 100%;
    height: 22px;
    margin-bottom: 30px;
    align-items: center;

    .headName {
      white-space: nowrap; /* 不换行 */
      overflow: hidden; /* 超出部分隐藏 */
      text-overflow: ellipsis; /* 显示省略号 */
    }

    .headImg {
      width: 22px;
      height: 22px;
      margin-right: 6px;
      border-radius: 50%;
      box-shadow: 0 1px 2px 0 rgb(0 0 0 / 10%);
    }
  }

  .returnMoneyBox {
    font-size: 14px;
    display: flex;
    height: 70%;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    .totalAmount,
    .matureTotalAmount {
      display: flex;
      width: 100%;
      height: 40%;
      flex-direction: column;
      justify-content: space-between;
    }

    .money {
      color: #888b98;
    }

    .goalRate {
      color: #fff;
      font-size: 18px;
      display: flex;
      width: max-content;
      min-width: 48px;
      min-height: 30px;
      padding: 0 6px;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      background: #05d380;
    }

    .tagGreen {
      background-color: #05d380;
    }

    .tagYellow {
      background-color: #ff9201;
    }

    .tagRed {
      background-color: #ff5219;
    }
  }

  .editBtn {
    color: #040919;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    width: 100%;
    height: 40px;
    justify-content: center;
    position: absolute;
    bottom: 0;
    left: 0;
    box-sizing: border-box;
    cursor: pointer;
    align-items: center;
    background: linear-gradient(0deg, rgb(214 231 255 / 30%), rgb(214 231 255 / 30%)),
      rgb(255 255 255 / 20%);
    border-top: 1px solid rgb(255 255 255 / 30%);
    backdrop-filter: blur(30px);
    box-shadow: 0 -2px 5px 0 rgb(0 9 44 / 3%);
    border-radius: 0 0 18px 18px;
  }

  // 没保存条件时的蒙版
  .mask {
    display: flex;
    width: 100%;
    height: 100%;
    position: absolute;
    flex-direction: column;
    align-items: center;
    top: 0;
    left: 0;
    z-index: 999;
    background: rgba(255 255 255 / 90%);
    box-sizing: border-box;
    backdrop-filter: blur(12px);
    border-radius: 18px;
    /* stylelint-disable property-no-vendor-prefix */
    /* stylelint-disable-next-line value-no-vendor-prefix */
    -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);

    .maskTitle {
      color: black;
      font-size: 14px;
      width: 100%;
      padding: 12px 0 0 12px;
      letter-spacing: 0.13em;
    }

    .maskBtn {
      color: #008cff;
      font-size: 16px;
      padding: 4px 10px;
      letter-spacing: 0.13em;
      position: absolute;
      top: 50%;
      left: 50%;
      border-radius: 532px;
      box-sizing: border-box;
      transform: translate(-50%, -50%);
      background: rgb(255 255 255 / 20%);
      border: 0.5px solid #008cff;
    }
  }
}

.spinWrap {
  width: 100%;

  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}

.small {
  .headBox {
    font-size: 12px;
    margin-bottom: 10px;
  }

  .returnMoneyBox {
    font-size: 10px;

    .totalAmount,
    .matureTotalAmount {
      height: 42%;
    }

    .goalRate {
      font-size: 14px;
      min-width: 36px;
      min-height: 24px;
      padding: 0 6px;
    }
  }

  .editBtn {
    font-size: 12px;
    height: 32px;
  }

  // 没保存条件时的蒙版
  .mask {
    .maskTitle,
    .maskBtn {
      font-size: 10px;
    }
  }
}
