import { createHttpRequest } from '@echronos/core';

/**
 * 团队季度预计回款--季度单选条件,部门多选条件,卡片查询条件保存
 */

function postQueryConditionSave(quarterList: number[], userList: number[], blockId: string) {
  return createHttpRequest('ech-crm-new')('/v2/home/<USER>/queryCondition/save', {
    method: 'POST',
    data: {
      type: 6,
      quarterList, // 指定团队回款数据分析--季度单选条件【第一季度-1，第二季度-2，第三季度-3，第四季度-4】
      userList, // 指定团队回款数据分析--成员多选条件
      cardUuid: blockId, // 卡片id
    },
    autoToast: false,
  });
}

export default postQueryConditionSave;
