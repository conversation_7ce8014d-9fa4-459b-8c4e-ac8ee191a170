import { createHttpRequest } from '@echronos/core';
// import { MemberListType } from '../compoment/api/index.js';

/**
 * 获取员工商机成熟度覆盖率卡片
 */

export interface MemberInfoType {
  memberId: number; // 成员ID
  memberName: string; // 成员名称
  userId: number; // 用户Id
  companyId: number; // 公司id
  avatar: string; // 成员头像
}

interface TunityMoneyType {
  money: string;
  goalRate?: string;
  holidayMoney?: string;
  changeMoney: string;
  changeType: number;
}

export interface MemberMatureCardType {
  memberList: MemberInfoType[]; // 员工信息列表
  opportunityMoney: TunityMoneyType; // 商机总金额
  matureOpportunityMoney: TunityMoneyType; // 成熟商机总金额
  deptStatisticsQuarter: number[]; // 季度列表
  userStatisticsList: number[]; // 员工id
}

function getMemberMatureCard(blockId: string): Promise<MemberMatureCardType> {
  return createHttpRequest('ech-crm-new')('/v2/home/<USER>/getMemberMatureCard', {
    method: 'GET',
    params: {
      cardUuid: blockId,
    },
    autoToast: false,
  });
}

export default getMemberMatureCard;
