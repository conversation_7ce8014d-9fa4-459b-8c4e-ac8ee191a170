import { createHttpRequest } from '@echronos/core';

/**
 * 获取指定团队回款数据分析卡片
 */

export interface DeptInfoType {
  deptId: number; // 团队ID
  deptName: string; // 团队名称
}

interface ReturnedMoneyDetailType {
  money: string; // 金额【单位：万元】
  goalRate?: string; // 占目标倍率
  holidayMoney?: string; // 历史对比金额【单位：万元】
  changeMoney: string; // 变化金额【单位：万元】
  changeType: number; // 变化类型 0-没变化，1-新增，2-减少
}

interface MonthReturnedMoneyMapType {
  [key: string]: ReturnedMoneyDetailType;
}

export interface DeptRefundType {
  deptStatisticsQuarter: number[]; // 季度
  deptStatisticsList: number[]; // 部门多选条件[deptId]
  deptList: DeptInfoType[]; // 部门列表
  targetMoney: string; // 目标金额【单位：万元】
  opportunityMoney: ReturnedMoneyDetailType; // 商机总金额分析【单位：万元】
  matureOpportunityMoney: ReturnedMoneyDetailType; // 成熟商机总金额分析
  returnedMoney: ReturnedMoneyDetailType; // 预计回款总金额分析
  monthList: string[]; // 统计月列表
  monthReturnedMoneyMap: MonthReturnedMoneyMapType; // 月预计回款金额合计 month -> money【单位：万元】
}

function getDeptStatisticsCard(blockId: string): Promise<DeptRefundType> {
  return createHttpRequest('ech-crm-new')('/v2/home/<USER>/getDeptStatisticsCard', {
    method: 'GET',
    params: {
      cardUuid: blockId,
    },
    autoToast: false,
  });
}
export default getDeptStatisticsCard;
