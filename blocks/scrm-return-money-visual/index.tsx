import React, { useEffect, useRef, useState } from 'react';
import { Progress } from 'antd';
import classNames from 'classnames';
import useElSize from '../utils/hooks/use-el-size';
import useLang from '../utils/hooks/use-lang';
import SelectionCondition, { getCurrentQuarter } from '../components/selection-condition';
import { IEchBlockCard } from '../interface';
import './index.less';
import CardContext from '../components/card-context';
import { postQueryConditionSave, getDeptStatisticsCard, DeptInfoType, DeptRefundType } from './api';
import zh from './locales/zh.json';
import en from './locales/en.json';

function ScrmReturnMoneyVisual({ navigate, blockId }: IEchBlockCard) {
  const t = useLang({
    zh,
    en,
  });
  const [loading, setLoading] = useState(false); // 数据加载

  const [editBtnShow, setEditBtnShow] = useState(false); // 编辑按钮
  const [editBoxShow, setEditBoxShow] = useState(false); // 编辑盒子
  const [maskShow, setMaskShow] = useState(false); // 蒙版显隐

  const [quarter, setQuarter] = useState<number[]>(getCurrentQuarter([])); // 季度
  const [deptId, setDeptId] = useState<number[]>([]); // 部门id
  const [deptList, setDeptList] = useState<DeptInfoType[]>([]); // 可选部门列表

  // 季度团队回款数据
  const [deptRefundData, setDeptRefundData] = useState<DeptRefundType | null>(null);
  // tag的颜色
  const tagColorClass = (type: number, goalRate: number) => {
    // goalRate 占目标倍率
    // type 1-商机总金额 2-成熟商机总金额
    if (goalRate < 1) {
      return 'tagRed';
    }
    if ((type === 1 && goalRate < 1.2) || (type === 2 && goalRate < 4)) {
      return 'tagYellow';
    }
    return 'tagGreen';
  };

  // 获取当前卡片的DOM元素
  const echBlockScrmDataAnalysisRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockScrmDataAnalysisRef, 324);

  const handleMouseEnter = () => {
    if (maskShow) return;
    setEditBtnShow(true);
  };

  const handleMouseLeave = () => {
    if (maskShow) return;
    setEditBtnShow(false);
  };

  // 获取卡片数据
  const getDeptStatisticsData = () => {
    getDeptStatisticsCard(blockId?.toString() || '')
      .then((res: DeptRefundType) => {
        // 如果没有保存卡片查询条件，则自动进入选择查询条件
        if (!res.deptStatisticsQuarter.length || !res.deptStatisticsList.length) {
          setMaskShow(true);
          return;
        }
        // 保存当前查询条件
        setDeptRefundData(res);
        setDeptList(res.deptList);
        setQuarter(res.deptStatisticsQuarter);
        setDeptId(res.deptStatisticsList);
        setEditBoxShow(false);
      })
      .finally(() => {
        setLoading(false);
        setEditBoxShow(false);
      });
  };

  // 完成
  const handleSuccess = (quarters: number[], deptIds: number[]) => {
    setLoading(true);
    postQueryConditionSave(quarters, deptIds, blockId?.toString() || '').finally(() =>
      getDeptStatisticsData()
    );
  };

  // 首次渲染时获取卡片数据
  useEffect(() => {
    getDeptStatisticsData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 跳转进入数据分析页面
  const onJumpPage = () => {
    if (maskShow) return;
    if (navigate) {
      navigate(`/scrm/client/report-forms?quarter=${deptRefundData?.deptStatisticsQuarter}`);
    }
  };

  // 数据可视化卡片
  return (
    <CardContext loading={loading} isPermission spinWrapperClassName="spinWrap">
      <div
        ref={echBlockScrmDataAnalysisRef}
        className={classNames('ech-block-scrm-dataAnalysis', { dis: editBoxShow, small: isSmall })}
        role="button"
        tabIndex={0}
        onClick={onJumpPage}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {!editBoxShow ? (
          <>
            <div className="titileName">
              {deptList.find((item) => item.deptId === deptId[0])?.deptName || '--'}
            </div>
            <div className="schedule">
              {/* 回款目标 */}
              <div className="typeAmount">
                <div className="title">
                  {t('desk_scrmReturnMoney_quarterTarget', {
                    quarter: deptRefundData?.deptStatisticsQuarter,
                  })}
                </div>
                <div className="amount">
                  <div className="money">
                    {deptRefundData?.targetMoney}{' '}
                    <span className="unit">{t('desk_scrmReturnMoney_unit')}</span>
                  </div>
                  <Progress
                    strokeColor={{
                      '0%': '#108ee9',
                      '100%': '#87d068',
                    }}
                    showInfo={false}
                    percent={100}
                    className="progressBar"
                  />
                </div>
              </div>
              {/* 商机总金额 */}
              <div className="typeAmount">
                <div className="title">
                  {t('desk_scrmReturnMoney_quarterOpportunity', {
                    quarter: deptRefundData?.deptStatisticsQuarter,
                  })}
                  <div
                    className={`tag ${tagColorClass(
                      2,
                      Number(deptRefundData?.opportunityMoney.goalRate)
                    )}`}
                  >
                    {deptRefundData?.opportunityMoney.goalRate}
                  </div>
                </div>
                <div className="amount">
                  <div className="money">
                    {deptRefundData?.opportunityMoney.money}{' '}
                    <span className="unit">{t('desk_scrmReturnMoney_unit')}</span>
                  </div>
                  <Progress
                    strokeColor={{
                      '0%': '#FB298B',
                      '100%': '#F7A677',
                    }}
                    showInfo={false}
                    percent={Number(deptRefundData?.opportunityMoney.goalRate) * 100}
                    className="progressBar"
                  />
                </div>
              </div>
              {/* A+B成熟商机总金额 */}
              <div className="typeAmount">
                <div className="title">
                  {t('desk_scrmReturnMoney_matureOpportunity')}
                  <div
                    className={`tag ${tagColorClass(
                      1,
                      Number(deptRefundData?.matureOpportunityMoney.goalRate)
                    )}`}
                  >
                    {deptRefundData?.matureOpportunityMoney.goalRate}
                  </div>
                </div>
                <div className="amount">
                  <div className="money">
                    {deptRefundData?.matureOpportunityMoney.money}{' '}
                    <span className="unit">{t('desk_scrmReturnMoney_unit')}</span>
                  </div>
                  <Progress
                    strokeColor={{
                      '0%': '#905FFF',
                      '100%': '#EA00FF',
                    }}
                    showInfo={false}
                    percent={Number(deptRefundData?.matureOpportunityMoney.goalRate) * 100}
                    className="progressBar"
                  />
                </div>
              </div>
              {/* 预计回款总金额 */}
              <div className="typeAmount">
                <div className="title">
                  {t('desk_scrmReturnMoney_quarterExpected', {
                    quarter: deptRefundData?.deptStatisticsQuarter,
                  })}
                </div>
                <div className="amount">
                  <div className="money">
                    {deptRefundData?.returnedMoney.money}{' '}
                    <span className="unit">{t('desk_scrmReturnMoney_unit')}</span>
                  </div>
                  <Progress
                    strokeColor={{
                      '0%': '#1C38DC',
                      '100%': '#49E5EB',
                    }}
                    showInfo={false}
                    percent={Number(deptRefundData?.returnedMoney.goalRate) * 100}
                    className="progressBar"
                  />
                </div>
              </div>
            </div>

            <div className="compare">
              {deptRefundData?.monthList.map((key) => (
                <div className="itemType" key={key}>
                  <div>
                    <span className="month">{t('desk_scrmReturnMoney_month', { month: key })}</span>
                    {deptRefundData?.monthReturnedMoneyMap[key].money}
                    {t('desk_scrmReturnMoney_unit')}
                  </div>
                  <div className="compareChange">
                    {t('desk_scrmReturnMoney_compareLastWeek')}
                    {deptRefundData?.monthReturnedMoneyMap[key].changeType === 0 && (
                      <span className="placeholder" />
                    )}
                    {deptRefundData?.monthReturnedMoneyMap[key].changeType === 1 && (
                      <img
                        src={`${
                          import.meta.env.BIZ_ORIGIN_STATIC_URL
                        }/static/img/722838622077979.png`}
                        alt=""
                        className="imgIcon"
                      />
                    )}
                    {deptRefundData?.monthReturnedMoneyMap[key].changeType === 2 && (
                      <img
                        src={`${
                          import.meta.env.BIZ_ORIGIN_STATIC_URL
                        }/static/img/1722525941759855.png`}
                        alt=""
                        className="imgIcon"
                      />
                    )}
                    {deptRefundData?.monthReturnedMoneyMap[key].changeType === 0
                      ? t('desk_scrmReturnMoney_noChange')
                      : t('desk_scrmReturnMoney_changeAmount', {
                          amount: deptRefundData?.monthReturnedMoneyMap[key].changeMoney,
                        })}
                  </div>
                </div>
              ))}
            </div>

            {/* 编辑按钮 */}
            {editBtnShow && (
              <div
                className="editBtn"
                role="button"
                tabIndex={0}
                onClick={(e) => {
                  e.stopPropagation();
                  setEditBoxShow(true);
                }}
              >
                {t('desk_scrmReturnMoney_edit')}
              </div>
            )}
          </>
        ) : (
          <SelectionCondition
            theme="dark"
            size={echBlockScrmDataAnalysisRef.current?.offsetWidth}
            isSmall={isSmall}
            className="editBox"
            memberName={t('desk_scrmReturnMoney_team')}
            radioQuarter
            visible={editBoxShow}
            quartered={quarter}
            memberType={1}
            radioMember
            memberIded={deptId}
            onSuccess={(quarters, deptIds) => {
              handleSuccess(quarters, deptIds || []);
            }}
          />
        )}

        {/* 蒙版 */}
        {maskShow ? (
          <div className="mask">
            <div className="maskTitle">{t('desk_scrmReturnMoney_maskTitle')}</div>
            <div
              className="maskBtn"
              role="button"
              tabIndex={0}
              onClick={(e) => {
                e.stopPropagation();
                setEditBoxShow(true);
                setMaskShow(false);
              }}
            >
              {t('desk_scrmReturnMoney_setConditions')}
            </div>
          </div>
        ) : null}
      </div>
    </CardContext>
  );
}

export default ScrmReturnMoneyVisual;
