import { CSSProperties, useMemo } from 'react';
import useLang from '../../utils/hooks/use-lang';
import { formatTime } from '../../utils/utils';
import zh from '../locales/zh.json';
import en from '../locales/en.json';

interface SendTimeProps {
  time: number; // 日期时间
}

const timeStyle: CSSProperties = {
  fontSize: '14px',
  display: 'flex',
  alignItems: 'center',
  gap: '4px',
  color: '#888B98',
};

function SendTime({ time }: SendTimeProps) {
  // 多语言导入
  const t = useLang({
    zh,
    en,
  });
  const formattime = useMemo(() => formatTime(time), [time]);

  return (
    <div style={timeStyle}>
      <img
        style={{ width: 16, height: 16 }}
        src={`${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1728633900983625.png`}
        alt="时间"
      />
      <span>{t('desk_uoumSendTime_publishTime')}: </span>
      <span>{formattime}</span>
    </div>
  );
}

export default SendTime;
