.cart {
  &MainContent {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  &TimeOutText {
    color: #888b98;
    font-size: 12px;
    line-height: 12px;
    padding: 4px;
    background-color: #f5f6fa;
  }

  &TimeContent {
    display: flex;
  }

  &MessageText {
    display: flex;
    gap: 24px;
  }

  &Time {
    display: flex;
    gap: 11px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &Text {
      background-color: #d9eeff;
      color: #008cff;
      font-size: 12px;
      display: flex;
      line-height: 12px;
      padding: 4px;
      align-items: center;
    }
  }

  &Title {
    color: #040919;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  &Item {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  &Tap {
    color: #888b98;
    font-size: 12px;
    background-color: #f5f6fa;
    padding: 2px 4px;
  }

  &MessageContent {
    display: flex;
    gap: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &CompanyName {
    color: #888b98;
    font-size: 12px;
    line-height: 22px;
  }
}

// 小尺寸样式
._324 {
  gap: 12px;

  .cart {
    &Title {
      font-size: 8px;
      line-height: 12px;
    }

    &Item {
      gap: 4px;
    }

    &Time {
      &Text {
        font-size: 7px;
        line-height: 6px;
      }

      &OutText {
        font-size: 8px;
        line-height: 8px;
        padding: 4px;
      }
    }

    &Tap {
      font-size: 8px;
      display: flex;
      align-items: center;
    }

    &MessageContent {
      height: 14px;

      img {
        width: 10px !important;
        height: 10px !important;
      }

      div {
        span {
          font-size: 8px;
          line-height: 15px;
        }
      }
    }

    &CompanyName {
      font-size: 8px;
      line-height: 15px;
    }
  }
}
