import { createHttpRequest, PaginationResponse } from '@echronos/core';

interface GetUoumProcurementNoticeParams {
  siteId: number; // 站点ID
  categoryIdList?: string[];
  categoryId?: string;
  pageSize?: number;
  pageNo?: number;
  trendsTypeList?: number[];
}
type CategoryList = {
  id: number; // 供应商分类ID
  name: string; // 供应商分类名称
  level: number; // 供应商分类等级
};

export type ProcurementNtionProp = {
  id: number; // 公示ID
  title: string; // 公示标题
  issuedTime: number; // 发布时间
  endTime: number; // 截止时间
  companyName: string; // 所属公司名称
  categoryList: CategoryList[]; // 分类列表
};

// 意向寻源和采购公告同一接口
export function getUoumProcurementNotice(
  data: GetUoumProcurementNoticeParams
): PaginationResponse<ProcurementNtionProp> {
  return createHttpRequest('ech-bidding')(`/v1/bidding/trendsSite/pageMainSheet`, {
    method: 'POST',
    data,
  });
}
