import { createHttpRequest, PaginationResponse } from '@echronos/core';

type GetUoumVendorRecruitmentParams = {
  moduleTypes: number[];
  pageNo: number;
  pageSize: number;
  tenantId: string;
  topicId: number;
};

export type VendorRecruitmentProp = {
  id: number; // 帖子/动态主键id
  title: string; // 文章标题
  createTime: number; // 创建时间
};

export function getUoumVendorRecruitment(
  data: GetUoumVendorRecruitmentParams
): PaginationResponse<VendorRecruitmentProp> {
  return createHttpRequest('ech-cms')(`/v1/cms/trends/publicity/page/list`, {
    method: 'POST',
    data,
  });
}
