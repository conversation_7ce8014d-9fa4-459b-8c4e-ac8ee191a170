.spinWrapper {
  width: 100%;
  height: 100%;
  background-image: url('https://static.huahuabiz.com/static/img/1724894979914341.png'),
    linear-gradient(180deg, #735efd 0%, #3a2de0 100%);
  background-position: 16px 24px, center;
  background-size: 208px 206px, cover;
  background-repeat: no-repeat, no-repeat;
}

.scrm_opportunity_total_amount {
  color: #fff;
  padding-top: 22px;
  padding-left: 100px;
}

.numbers {
  font-size: 34px;
  font-weight: 500;
  display: block;
  height: 48px;
  line-height: 48px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.smallNum {
  font-size: 28px;
}

.labelName {
  font-size: 18px;
  height: 26px;
  line-height: 26px;
}

// 小卡片样式
.small {
  &.scrm_opportunity_total_amount {
    padding-top: 14px;
    padding-left: 64px;

    .numbers {
      font-size: 24px;
      height: 30px;
      line-height: 24px;
    }

    .smallNum {
      font-size: 20px;
    }

    .labelName {
      font-size: 10px;
      line-height: 1;
    }
  }
}
