import React, { HTMLAttributes, useRef, useMemo, useEffect, memo } from 'react';
import { useRequest } from 'ahooks';
import { Spin, Carousel } from '@echronos/antd';
import classNames from 'classnames';
import useElSize from '../utils/hooks/use-el-size';
import getDesktopMyAppBanner from './apis/get-desktop-my-app-banner';
import style from './index.module.less';
import './mobile.less';

interface BannerPictureType {
  id: string;
  url: string;
  link: {
    label: string;
    type: string;
    url: string;
  };
}

export type BannerProps = HTMLAttributes<HTMLDivElement> & {
  data: {
    abbreviation: string;
    companyName: string;
    companyNature: number;
    id: number;
    isAdministrator: number;
    isDeleted: number;
    logoUrl: string;
    picture: string;
    status: number;
  };
};

const defaultBannerUrl = `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/169294497619674.png`;

function Banner() {
  const ref = useRef(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(ref.current, 324);
  // @ts-ignore
  const { data, loading, run }: { data: any; loading: boolean; run: () => void } = useRequest(
    () => getDesktopMyAppBanner().then((res: any) => res),
    {
      manual: true,
    }
  );

  const bannerList = useMemo<BannerPictureType[]>(() => {
    let JsonBannar = [];
    if (data?.picture) {
      try {
        JsonBannar = JSON.parse(data.picture);
      } catch (error) {
        JsonBannar = [{ id: '1', url: defaultBannerUrl, link: { type: '', url: '', label: '' } }];
      }
      return JsonBannar;
    }
    return [{ id: '1', url: defaultBannerUrl, link: { type: '', url: '', label: '' } }];
  }, [data?.picture]);

  const isShowCompanyName = useMemo(() => {
    if (data) {
      return data?.abbreviation || data?.companyName;
    }
    return false;
  }, [data]);

  const isUrlFn = (item: BannerPictureType) => {
    const isUrl =
      item.link?.url.indexOf('http://') === 0 || item.link?.url.indexOf('https://') === 0;
    return item.link?.type === 'web' && item.link?.url && isUrl;
  };

  useEffect(() => {
    run();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div
      ref={ref}
      className={classNames(style.echBlockBanner, 'ech-block-m-banner', { [style.small]: isSmall })}
    >
      <Spin spinning={loading}>
        <Carousel autoplay={bannerList.length > 1}>
          {bannerList.map((item) => (
            <div
              key={item.id}
              role="presentation"
              className={classNames({ [style.isUrlFnStyle]: isUrlFn(item) })}
              onClick={() => {
                if (isUrlFn(item)) {
                  window.open(item.link.url);
                }
              }}
            >
              <div
                className={style.bannerBgItem}
                style={{
                  backgroundImage: `url('${item.url}')`,
                }}
              />
            </div>
          ))}
        </Carousel>
        {bannerList.length === 1 && bannerList[0].url === defaultBannerUrl && isShowCompanyName && (
          <div className={style.companyName}>{data.abbreviation || data.companyName}</div>
        )}
      </Spin>
    </div>
  );
}

export default memo(Banner);
