.indexModal {
  :global {
    width: 256px;
    height: 254px;
    padding: 12px;
    overflow: hidden;
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 999999;
    transform: translate(-50%, -50%);
    background: #fff;
    border-radius: 18px;
  }
}

.indexModalHead {
  :global {
    display: flex;
    width: 100%;
    margin-bottom: 16px;
    justify-content: space-between;
    align-items: center;
  }
}

.indexModalHeadLeft {
  :global {
    display: flex;
    align-items: center;
  }
}

.indexModalLogo {
  :global {
    width: 22px;
    height: 22px;
  }
}

.indexModalTitle {
  :global {
    color: #040919;
    font-size: 15px;
    font-weight: 500;
    margin-left: 4px;
  }
}

.successBtn {
  :global {
    font-size: 12px;
    width: 40px;
    height: 18px;
    line-height: 16px;
    border-radius: 4px;
    cursor: pointer;
    border: 0.5px solid rgb(0 0 0 / 10%);
    text-align: center;
  }
}

.indexModalDesc {
  :global {
    color: rgb(4 9 25 / 70%);
    font-size: 12px;
  }
}

.indexModalContent {
  :global {
    display: flex;
    height: 34px;
    padding: 0 12px;
    justify-content: space-between;
    background: #f5f6fa;
    align-items: center;
    border-radius: 6px;
  }
}

.indexModalContentName {
  :global {
    width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.indexModalContentSelect {
  :global {
    color: #008cff;
    width: 100px;
    overflow: hidden;
    cursor: pointer;
    border-radius: 6px;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: right;
  }
}

.meun {
  :global {
    width: 220px;
    height: 215px;
    // overflow-y: auto;
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 9999;
    transform: translate(-50%, -50%);
    border-radius: 14px;
    background: #fff;
    border: 1px solid rgb(0 0 0 / 15%);
    box-shadow: 8px 6px 20px 0 rgb(2 9 58 / 12%), -8px 6px 20px 0 rgb(2 9 58 / 12%);
  }
}

.meunItem {
  :global {
    display: flex;
    height: 34px;
    padding: 0 12px;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    border-bottom: 1px solid #f5f6fa;
    cursor: pointer;
  }

  &:hover {
    background: #d8e9f7;
  }
}

.meunTitle {
  :global {
    color: #040919;
    font-size: 12px;
    font-weight: 500;
    height: 28px;
    line-height: 28px;
    text-align: center;
    font-family: PingFangSC-Medium, sans-serif;
  }
}

.meunSearch {
  :global {
    display: flex;
    padding: 0 12px 8px;
    justify-content: space-between;
    align-items: center;
  }
}

.meunSearchInput {
  :global {
    width: 164px;
    height: 30px;
  }
}

.meunSearchCancel {
  :global {
    cursor: pointer;
    color: #008cff;
  }
}

.meunList {
  :global {
    max-height: 144px;
    padding-left: 4px;
    overflow-y: auto;
  }
}

.meunItemContent {
  :global {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

// 小尺寸的卡片样式
._150,
._324 {
  &.indexModal {
    width: 100%;
    height: 100%;
  }

  .indexModalLogo {
    width: 18px;
    height: 18px;
  }

  .indexModalTitle {
    font-size: 10px;
    display: inline-block;
    height: 18px;
    line-height: 18px;
    margin-left: 2px;
  }

  .successBtn {
    font-size: 8px;
    width: 40px;
    height: 18px;
    line-height: 18px;
  }

  .indexModalContent {
    height: 28px;
    padding: 0 12px;
  }

  .indexModalDesc,
  .indexModalContentName,
  .indexModalContentSelect {
    font-size: 8px;
  }

  .meun {
    width: 100%;
    height: 100%;
    top: 50%;
  }

  .meunItem {
    font-size: 8px;
    height: 27px;
    line-height: 27px;
  }

  .meunTitle {
    font-size: 10px;
    height: 28px;
    line-height: 28px;
  }

  .meunSearchCancel {
    font-size: 10px;
  }

  .meunSearch {
    line-height: 25px;
    padding: 0 12px 4px;
  }

  .meunSearchInput {
    font-size: 10px;
    width: 98px;
    height: 24px;
    padding: 4px 10px;

    :global {
      .ant-input {
        font-size: 8px;
      }
    }
  }

  .meunItemContent {
    font-size: 8px;
  }
}

._324 {
  &.indexModal {
    width: 160px;
    height: 100%;
  }

  .meunSearchInput {
    width: 110px;
    height: 26px;
  }
}
