import { createHttpRequest } from '@echronos/core';

export interface ParamsType {
  mainSheetId: number;
  roleType: 1 | 2;
  childSheetId?: number;
}
function editSave(data: {
  // type: number;
  flowInfoId?: number | string;
  statisticsFlowInfoId?: number | string;
  cardUuid: number | undefined;
}) {
  return createHttpRequest('ech-bidding')('/v1/home/<USER>/queryCondition/save', {
    method: 'POST',
    data,
  });
}

function getQueryCondition(params: { cardUuid: number | undefined }): Promise<any> {
  return createHttpRequest('ech-bidding')('/v1/home/<USER>/queryCondition/get', {
    method: 'GET',
    params,
  });
}

function getFlowTitleList(params: { flowInfoId: number }): Promise<any> {
  return createHttpRequest('ech-bidding')('/v1/bidding/main/flow/title/list', {
    method: 'GET',
    params,
  });
}

function getBiddingStep(params: ParamsType): Promise<any> {
  return createHttpRequest('ech-bidding')('/v1/bidding/main/flow/title/list', {
    method: 'GET',
    params,
  });
}
function getBiddingList(data: { flowInfoId: number; pageNo: number; pageSize: number }) {
  return createHttpRequest('ech-bidding')('/v1/bidding/main/list', {
    method: 'POST',
    data,
  });
}
function jumptoSheet(params: ParamsType): Promise<any> {
  return createHttpRequest('ech-bidding')('/v1/bidding/sheet/flow/list', {
    method: 'GET',
    params,
  });
}
function receiveOrderCard(): Promise<any> {
  return createHttpRequest('ech-psi')('/v1/platform/desk/card/receiveOrderCard', {
    method: 'GET',
  });
}
function planCard(data: { flowInfoId: number; pageNo: number; pageSize: number }): Promise<any> {
  return createHttpRequest('ech-bidding')('/v1/home/<USER>/planCard', {
    method: 'POST',
    data,
    autoToast: false,
  });
}

export {
  editSave,
  getQueryCondition,
  getFlowTitleList,
  getBiddingStep,
  getBiddingList,
  jumptoSheet,
  receiveOrderCard,
  planCard,
};
