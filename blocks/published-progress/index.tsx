import React, { ReactNode, useCallback, useEffect, useMemo, useRef } from 'react';
import { message } from 'antd';
import { isFunction } from 'lodash';
import classNames from 'classnames';
import { useSize } from 'ahooks';
import { IEchBlockCard } from '../interface';
import useLang from '../utils/hooks/use-lang';
import useElSize from '../utils/hooks/use-el-size';
import style from './index.module.less';
import IndexModal from './indexModal';
import {
  editSave,
  getQueryCondition,
  getFlowTitleList,
  // getBiddingStep,
  getBiddingList,
  jumptoSheet,
  planCard,
} from './api';
import zh from './locales/zh.json';
import en from './locales/en.json';
import { getFlowInfoList } from '../procurement-view/api';
import NoPermissionCard from '../components/no-permission-card';

interface flowInfoListType {
  name: string;
  value: string;
  checked: boolean;
}
function PublishedProgress({ navigate, blockId }: IEchBlockCard) {
  // 多语言导入
  const t = useLang({
    zh,
    en,
  });
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [cancel, setCancel] = React.useState(false);
  const [flowInfoList, setFlowInfoList] = React.useState<Array<flowInfoListType>>([]);
  const [saveflowInfoList, setSaveFlowInfoList] = React.useState<Array<flowInfoListType>>([]);
  const [checkedData, setCheckedData] = React.useState({ value: 0, name: '' });
  const saveFlowInfoList = useRef<Array<flowInfoListType>>([]);
  const [, setCount] = React.useState(0);
  const [stepList, setStepList] = React.useState<any[]>([]);
  const [stepStatus, setStepStatus] = React.useState<any[]>([]);
  const [isPermission, setIsPermission] = React.useState(false);

  // 获取当前卡片的DOM元素
  const echBlockPublishedProgressRef = useRef<HTMLDivElement>(null);
  const iconRef = useRef<HTMLSpanElement>(null);
  const progressItem = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockPublishedProgressRef, 324);
  const iconSize = useSize(iconRef);
  const progressItemSize = useSize(progressItem);

  // const navigate = useNavigate();
  const handleOk = () => {
    setIsModalOpen(false);
    editSave({ cardUuid: Number(blockId), flowInfoId: checkedData.value })
      .then(() => getQueryCondition({ cardUuid: blockId }))
      .then(() => {
        // eslint-disable-next-line no-use-before-define
        publicFun(checkedData);
      })
      .catch(() => {
        message.error('编辑失败');
      });
  };
  const handleOpenModal = (e: { stopPropagation: () => void }) => {
    e.stopPropagation();
    setIsModalOpen(true);
  };
  const cancelSearch = () => {
    setCancel(false);
  };
  const checkStatus = (val: { value: number; name: string }) => {
    const data = saveflowInfoList.map((item: any) =>
      item.value === val.value
        ? { ...item, checked: true }
        : {
            ...item,
            checked: false,
          }
    );
    setFlowInfoList(data);
    setCancel(false);
    setFlowInfoList(saveflowInfoList);
    setSaveFlowInfoList(data);
    setCheckedData(val);
  };
  const getSearchResult = (e: { target: { value: string } }) => {
    // const t = flowInfoList.filter((item) => item.name.includes(e.target.value));
    const t1 = saveflowInfoList.filter((item) =>
      item.name.toLowerCase().includes(e.target.value.toLowerCase())
    );
    if (e.target.value === '') {
      setFlowInfoList(saveflowInfoList);
    } else {
      setFlowInfoList(t1);
    }
  };
  const selectPurchaseType = () => {
    setCancel(true);
    setFlowInfoList([...saveflowInfoList]);
  };
  // function uniqueFunc(arr, uniId){
  //   const res = new Map();
  //   return arr.filter((item) => !res.has(item[uniId]) && res.set(item[uniId], 1));
  // }
  const publicFun = (data: { value: number }) => {
    planCard({ pageNo: 1, pageSize: 2, flowInfoId: data.value })
      .then(() => {
        // console.log(rp);
      })
      .catch((err: { code: number }) => {
        if (err.code === 5) {
          setIsPermission(true);
        }
      });
    getBiddingList({ pageNo: 1, pageSize: 2, flowInfoId: data.value })
      .then((rp: { pagination: { count: number }; list: any[] }) => {
        setCount(rp.pagination.count);
        setStepList(rp.list);
        // console.log(rp);
      })
      .catch((err: { code: number }) => {
        if (err.code === 5) {
          setIsPermission(true);
        }
      });
    getFlowTitleList({ flowInfoId: data.value }).then((response: any) => {
      setStepStatus(response.list);
    });
  };

  const calculateProgressValue = (progressItemWidth: number, iconWidth: number) =>
    progressItemWidth - iconWidth;
  const calculateLeftOffSet = (iconWidth: number) => iconWidth / 2;
  // 计算进度条的长度和偏移量
  const calculateProgress = useMemo(() => {
    if (progressItemSize?.width && iconSize?.width) {
      return {
        progress: calculateProgressValue(progressItemSize.width, iconSize.width),
        leftOffSet: calculateLeftOffSet(iconSize.width),
      };
    }
    return {};
  }, [progressItemSize, iconSize]);

  // 计算函数 [(每一栏目的宽度 - 图标宽度) = 进度条长度]
  // useEffect(() => {
  //   if (progressItemSize?.width && iconSize?.width) {
  //     setProgress(calculateProgress);
  //   }
  // }, [progressItemSize, iconSize, calculateProgress]);
  useEffect(() => {
    getQueryCondition({ cardUuid: blockId }).then((t1: { flowInfoId: number }) => {
      // const p = { value: t.flowInfoId, name: '' };
      // publicFun(p);

      getFlowInfoList().then((res) => {
        const data = res.list.map(
          (item: { flowInfoName: string; flowInfoId: number }, index: number) => {
            if (t1.flowInfoId && item.flowInfoId === t1.flowInfoId) {
              const i = { value: item.flowInfoId, name: item.flowInfoName };
              setCheckedData(i);
              publicFun(i);
              return {
                name: item.flowInfoName,
                value: item.flowInfoId,
                checked: true,
              };
            }
            if (!t1.flowInfoId && index === 0) {
              const i = { value: item.flowInfoId, name: item.flowInfoName };
              setCheckedData(i);
              publicFun(i);
              return {
                name: item.flowInfoName,
                value: item.flowInfoId,
                checked: true,
              };
            }
            return {
              name: item.flowInfoName,
              value: item.flowInfoId,
              checked: false,
            };
          }
        );
        setFlowInfoList(data);
        setSaveFlowInfoList(data);
        saveFlowInfoList.current = data;
      });
    });
  }, [blockId]);
  const toBidPage = (item: any) => {
    const { id } = item;
    const codeToUrl: Record<string, string> = {
      '10001': `/bidding/bidding/plan/${id}`,
      '10002': `/bidding/bidding/notice/${id}`,
      '10003': `/bidding/bidding/document/${id}`,
      '10004': `/bidding/bidding/qualifications/${id}`,
      '10005': `/bidding/bidding/issuing/${id}`,
      '10006': `/bidding/bidding/response/${id}`,
      '10007': `/bidding/bidding/evaluation/${id}`,
      '10008': `/bidding/bidding/negotiation/${id}`,
      '10009': `/bidding/bidding/selection/${id}`,
      '10010': `/bidding/bidding/sign/${id}`,
    };
    jumptoSheet({
      mainSheetId: id,
      roleType: 1,
    }).then((res) => {
      if (isFunction(navigate)) {
        navigate(codeToUrl[res.currentFlowCode]);
      }
    });
  };
  const getStepNode = useCallback((arg: any | undefined): ReactNode => {
    // const titleNode: ReactNode = null;
    let statusClass = '';
    if (arg?.finishStatus) statusClass = `planStatus${arg.finishStatus}`;
    else if (arg?.isFinish) statusClass = 'planStatus1';
    else statusClass = 'planStatus4';
    // const iconNode: ReactNode = (
    //   <span className={style.stepIcon}>
    //     <span className={style[statusClass]} />
    //   </span>
    // );
    const iconNode: ReactNode = <span ref={iconRef} className={style[statusClass]} />;
    return iconNode;
  }, []);
  // const progress = [
  //   { name: '资格审核', background: 'linear-gradient(180deg, #23F09D 0%, #009056 100%)' },
  //   { name: '发标', background: 'linear-gradient(180deg, #23f09d 0%, #009056 100%)' },
  //   { name: '回标', background: 'linear-gradient(180deg, #ffd16a 0%, #e07400 100%)' },
  //   { name: '评标', background: 'linear-gradient(180deg, #ffb2b2 0%, #e00000 100%)' },
  //   { name: '商务谈判', background: 'linear-gradient(180deg, #f1f1f1 0%, #bfbfbf 100%)' },
  //   { name: '定标', background: 'linear-gradient(180deg, #bcbcbc 0%, #1e1e1e 100%)' },
  //   { name: '签约', background: 'linear-gradient(180deg, #f1f1f1 0%, #bfbfbf 100%)' },
  // ];
  // const cssFun = (length: number) => {
  //   if (length === 4) {
  //     return 90;
  //   }
  //   if (length === 5) {
  //     return 70;
  //   }
  //   if (length === 6) {
  //     return 55;
  //   }
  //   return 50;
  // };
  return (
    <div
      className={classNames(style.outSide, { [style.small]: isSmall, [style.hidden]: isModalOpen })}
      ref={echBlockPublishedProgressRef}
    >
      {!isModalOpen ? (
        <>
          <div className={style.insideLeft}>
            <img
              src={`${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1750065497092.svg`}
              alt=""
              className={style.logo}
            />
            <span className={style.title}>{t('desk_progress_title')}</span>
            <span className={style.procurementName}>
              {checkedData.name || t('desk_progress_methodPlaceholder')}
            </span>
            <span
              role="presentation"
              className={style.more}
              onClick={() => {
                if (isFunction(navigate)) {
                  navigate('/bidding/bidding/list');
                }
              }}
            >
              {t('desk_progress_viewMore')}
            </span>
          </div>
          <div className={style.insideRight}>
            {stepList.length > 0 ? (
              stepList.map((item) => (
                <div key={item.id} className={style.stepItem}>
                  <p className={style.companyName}>
                    {item.companyName || t('desk_progress_defaultProject')}
                  </p>
                  <div className={style.insideBottom}>
                    {stepStatus.map((stepItem, index) => (
                      <div
                        ref={progressItem}
                        className={style.progressItem}
                        key={stepItem.flowName}
                      >
                        <span className={style.flowName}>{stepItem.flowName}</span>
                        <div
                          className={style.progress}
                          role="presentation"
                          onClick={() => {
                            toBidPage(item);
                          }}
                        >
                          {getStepNode(item.flowSnapInfoMap[stepItem.flowCode])}
                          {index === stepStatus.length - 1 ? null : (
                            <hr
                              className={style.progressLine}
                              style={{
                                width: `${calculateProgress.progress}px`,
                                transform: `translateX(${calculateProgress.leftOffSet}px)`,
                              }}
                            />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))
            ) : (
              <div className={style.noData}>{t('desk_progress_noData')}</div>
            )}
          </div>
        </>
      ) : (
        <IndexModal
          size={echBlockPublishedProgressRef.current?.offsetWidth}
          isSmall={isSmall}
          isModalOpen={isModalOpen}
          handleOk={handleOk}
          checkStatus={checkStatus}
          cancelSearch={cancelSearch}
          selectPurchaseType={selectPurchaseType}
          cancel={cancel}
          meun={flowInfoList}
          getSearchResult={getSearchResult}
          checkedValue={checkedData}
        />
      )}
      {!isPermission && !isModalOpen ? (
        <div className={style.editBtn} onClick={handleOpenModal} role="button" tabIndex={0}>
          {t('desk_progress_edit')}
        </div>
      ) : null}

      {isPermission ? (
        <NoPermissionCard
          appName={t('desk_progress_title')}
          isSmall={isSmall}
          size={echBlockPublishedProgressRef.current?.offsetWidth}
        />
      ) : null}
    </div>
  );
}

export default PublishedProgress;
