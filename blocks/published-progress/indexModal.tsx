/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react/require-default-props */
import React from 'react';
import { Input, Spin } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import useLang from '../utils/hooks/use-lang';
import zh from './locales/zh.json';
import en from './locales/en.json';
import style from './indexModal.module.less';

interface IProps {
  isModalOpen: boolean;
  handleOk: () => void;
  selectPurchaseType: () => void;
  cancelSearch?: () => void;
  checkStatus?: (val: any) => void;
  getSearchResult?: (val: any) => void;
  // changeSearchResult?: (val: any) => void;
  cancel: boolean;
  meun: any;
  checkedValue: any;
  isSmall?: boolean;
  size?: number;
}

function IndexModal({
  isModalOpen = false,
  handleOk = () => {},
  selectPurchaseType = () => {},
  cancelSearch = () => {},
  checkStatus = () => {},
  getSearchResult = () => {},
  // changeSearchResult = () => {},
  checkedValue = undefined,
  cancel = false,
  meun = [],
  size,
  isSmall,
}: IProps) {
  // 多语言导入
  const t = useLang({
    zh,
    en,
  });

  return (
    <div>
      {isModalOpen ? (
        <div
          className={classNames(style.indexModal, {
            [style[`_${size}`]]: isSmall,
          })}
        >
          <div className={style.indexModalHead}>
            <div className={style.indexModalHeadLeft}>
              <img
                src={`${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722998468679472.png`}
                alt=""
                className={style.indexModalLogo}
              />
              <span className={style.indexModalTitle}>{t('desk_procurementModal_title')}</span>
            </div>
            <div onClick={handleOk} tabIndex={0} role="button" className={style.successBtn}>
              {t('desk_procurementModal_done')}
            </div>
          </div>
          <p className={style.indexModalDesc}>{t('desk_procurementModal_description')}</p>
          <div className={style.indexModalContent}>
            <span className={style.indexModalContentName}>
              {t('desk_procurementModal_methodName')}
            </span>
            <span
              role="presentation"
              className={style.indexModalContentSelect}
              onClick={selectPurchaseType}
            >
              {checkedValue.name || t('desk_procurementModal_select')}
            </span>
          </div>
          {cancel ? (
            <div
              className={style.meun}
              role="presentation"
              onMouseDown={(e) => {
                e.stopPropagation();
              }}
            >
              <div className={style.meunTitle}>{t('desk_procurementModal_method')}</div>
              <div className={style.meunSearch}>
                <Input
                  className={style.meunSearchInput}
                  placeholder={t('desk_procurementModal_searchPlaceholder')}
                  prefix={<SearchOutlined />}
                  onInput={(e) => {
                    getSearchResult(e);
                  }}
                />
                <span
                  role="presentation"
                  className={style.meunSearchCancel}
                  onClick={() => {
                    cancelSearch();
                  }}
                >
                  {t('desk_procurementModal_cancel')}
                </span>
              </div>
              <div className={style.meunList} onWheel={(e) => e.stopPropagation()}>
                <Spin spinning={meun.length < 0}>
                  {meun.length > 0
                    ? meun.map((item: any) => (
                        <div
                          key={item.value}
                          role="button"
                          tabIndex={0}
                          className={style.meunItem}
                          onClick={() => {
                            checkStatus(item);
                          }}
                        >
                          <span className={style.meunItemContent}>{item.name}</span>
                          {item.checked ? (
                            <img
                              src={`${
                                import.meta.env.BIZ_ORIGIN_STATIC_URL
                              }/static/img/noAppIcon.svg`}
                              alt=""
                            />
                          ) : null}
                        </div>
                      ))
                    : null}
                </Spin>
              </div>
            </div>
          ) : null}
        </div>
      ) : null}
    </div>
  );
}

export default IndexModal;
