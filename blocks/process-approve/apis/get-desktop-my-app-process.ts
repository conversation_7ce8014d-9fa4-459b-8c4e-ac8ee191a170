import { createHttpRequest, PaginationResponse } from '@echronos/core';
import { IMyApp } from './model';

/**
 * 获取首页-功能性组件2-flowEngine列表
 * @param data
 */
function getDesktopMyAppProcess(): PaginationResponse<IMyApp> {
  return createHttpRequest('ech-system')('/v2/desktop/home', {
    data: {},
    method: 'GET',
    autoToast: false,
  }).then((res: { list: IMyApp[] }) => ({
    list: res.list.filter((cardItem) => cardItem.type === 2 && cardItem.coding === 'flowEngine'),
  }));
}

export default getDesktopMyAppProcess;
