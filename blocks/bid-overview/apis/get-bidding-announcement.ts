import { createHttpRequest } from '@echronos/core';

export interface IBiddingAnnouncement {
  isEnrollCount: number;
  loseBidCount: number;
  tenderingCount: number;
  tenderedCount: number;
  toBeTenderCount: number;
  winBidCount: number;
}

/**
 * 投标概览
 */
function getBiddingAnnouncement(): Promise<IBiddingAnnouncement> {
  return createHttpRequest('ech-bidding')('/v1/bidding/home/<USER>');
}

export default getBiddingAnnouncement;
