import React, { useEffect, useState } from 'react';
import Icon from '@echronos/echos-icon';
import Card<PERSON>eaderTitle from '../components/card-header-title';
import Echarts from '../components/echarts/index';
import useEcharts from '../utils/hooks/use-echarts';
import useLang from '../utils/hooks/use-lang';
import { IEchBlockCard } from '../interface';
import getBiddingAnnouncement from './apis/get-bidding-announcement';
import zh from './locales/zh.json';
import en from './locales/en.json';
import styles from './index.module.less';

function BidOverview({ navigate }: IEchBlockCard) {
  // 多语言导入
  const t = useLang({
    zh,
    en,
  });
  const echartsRef = useEcharts();
  const [total, setTotal] = useState(0);

  useEffect(() => {
    getBiddingAnnouncement().then((res) => {
      echartsRef.current?.setOption({
        legend: {
          right: 'right',
        },
        grid: {
          left: '1%', // 留出边距
          right: '1%',
          containLabel: true, // 关键属性，包含标签区域
        },

        series: [
          {
            name: 'Nightingale Chart',
            type: 'pie',
            radius: [10, 60],
            center: ['30%', '50%'],
            roseType: 'area',
            itemStyle: {
              borderRadius: 4,
            },
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: res.tenderingCount + 1, name: `投标中：${res.tenderingCount}` },
              { value: res.tenderedCount + 1, name: `已投标：${res.tenderedCount}` },
              { value: res.winBidCount + 1, name: `已中标：${res.winBidCount}` },
              {
                value: res.isEnrollCount + res.toBeTenderCount + res.loseBidCount + 1,
                name: `其他：${res.isEnrollCount + res.toBeTenderCount + res.loseBidCount}`,
              },
            ],
          },
        ],
      });
      setTotal(
        res.isEnrollCount +
          res.toBeTenderCount +
          res.loseBidCount +
          res.tenderingCount +
          res.tenderedCount +
          res.winBidCount
      );
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className={styles.bidOverview}>
      <CardHeaderTitle
        logo="https://static.huahuabiz.com/static/img/1722998468679472.png"
        cardName={t('bid_overview')}
      />
      <div className={styles.container}>
        <div className={styles.echartsBox}>
          <Echarts ref={echartsRef} id="echart" style={{ height: '100%', width: '100%' }} />
        </div>

        <div
          className={styles.bottomHandle}
          onClick={() => {
            navigate('/bidding/bid/list?keyword=&pageNo=1');
          }}
        >
          <div>参与的投标</div>
          <div className={styles.total}>
            {total}个
            <Icon
              name="right_arrow_line"
              size={16}
              style={{
                verticalAlign: 'text-top',
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default BidOverview;
