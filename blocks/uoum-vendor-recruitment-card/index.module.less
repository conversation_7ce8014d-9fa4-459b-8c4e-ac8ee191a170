.cart {
  &Item {
    font-size: large;
    display: flex;
    flex-direction: column;
    gap: 16px;

    &Content {
      display: flex;
      flex-direction: column;
    }
  }

  &MainTitle {
    color: #040919;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
  }
}

// 小尺寸样式
.small {
  gap: 10px;

  .cart {
    &Item {
      &Content {
        > div {
          font-size: 8px !important;
          line-height: 11px;

          img {
            width: 10px !important;
            height: 10px !important;
          }
        }
      }
    }

    &MainTitle {
      font-size: 8px;
      line-height: 18px;
    }
  }
}
