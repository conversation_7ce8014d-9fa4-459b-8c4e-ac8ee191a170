.disNewChartCard {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  background: rgba(255 255 255 / 70%);
  backdrop-filter: blur(25px);

  .title {
    display: flex;
    padding: 12px;

    .titleLogo {
      width: 22px;
      height: 22px;
      margin-right: 4px;
    }

    .titleName {
      color: #040919;
      margin-right: 4px;
    }
  }

  .content {
    width: 100%;
    flex: 1;
    margin-top: 20px;
    padding: 0 12px;

    .rankItem {
      color: #fff;
      display: flex;
      width: 100%;
      height: 50px;
      margin-bottom: 11px;
      padding: 0 8px;
      background: linear-gradient(270deg, #501dd0 0%, #3f2387 75%);
      backdrop-filter: blur(25.6px);
      border-radius: 8px;

      .rankItemImage {
        width: 40px;
        height: 40px;
      }

      .rankItemName {
        display: flex;
        width: calc(100% - 64px);
        align-items: center;
        margin-right: 4px;
        margin-left: 4px;
        white-space: nowrap; /* 防止文本换行 */
        overflow: hidden; /* 隐藏溢出的内容 */
        text-overflow: ellipsis; /* 当内容溢出时显示省略号 */
        cursor: pointer;
      }

      .rankItemPrice {
        display: flex;
        width: 125px;
        align-items: center;
        white-space: nowrap; /* 防止文本换行 */
        overflow: hidden; /* 隐藏溢出的内容 */
        text-overflow: ellipsis; /* 当内容溢出时显示省略号 */
        justify-content: flex-end;
        cursor: pointer;
      }
    }
  }

  .empty {
    color: #040919;
    display: flex;
    width: 100%;
    flex: 1;
    justify-content: center;
    align-items: center;
  }
}

.spinWrap {
  height: 100%;

  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}
