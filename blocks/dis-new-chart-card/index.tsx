import React, { useState, useEffect, useCallback } from 'react';
import { IEchBlockCard } from '../interface';
import Echarts, { EchartsProps } from '../components/echarts/index';
import useEcharts from '../utils/hooks/use-echarts';
import useLang from '../utils/hooks/use-lang';
import CardContext from '../components/card-context';
import zh from './locales/zh.json';
import en from './locales/en.json';
import styles from './index.module.less';

export type DisNewChartCardProps = IEchBlockCard & {
  appName?: string;
};

interface DataChartState {
  xData: string[];
  valueData: number[];
}

function DisNewChartCard({ appName }: DisNewChartCardProps) {
  const echartsRef = useEcharts();

  const [isPermission, setIsPermission] = useState(true);
  const [showLoading, setShowLoading] = useState(false);

  const [dataChartState, setDataChartState] = useState<DataChartState>({
    xData: ['1', '2', '3'],
    valueData: [300, 400, 600],
  });
  // 多语言导入
  const t = useLang({
    zh,
    en,
  });

  const echartsOption: EchartsProps['option'] = useCallback(
    () => ({
      color: ['#80FFA5'],
      tooltip: {
        trigger: 'axis',
      },
      grid: {
        left: '18px',
        right: '25px',
        bottom: '3%',
        top: '30px',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: dataChartState.xData,
        },
      ],
      yAxis: [
        {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
            },
          },
        },
      ],
      series: [
        {
          // name: 'Line 1',
          type: 'line',
          stack: 'Total',
          smooth: true,
          // symbol: 'image://https://img.huahuabiz.com/user_files/2022721/1658382551507866.svg',
          symbol: `image://${
            import.meta.env.BIZ_ORIGIN_STATIC_URL
          }/static/img/1723082014658233.png`,
          symbolSize: 10,
          lineStyle: {
            width: 4,
            color: '#008CFF',
            // color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            //   {
            //     offset: 0,
            //     color: '#6642F9',
            //   },
            //   {
            //     offset: 0.5,
            //     color: '#713EF0',
            //   },
            //   {
            //     offset: 0.75,
            //     color: '#EC0A8D',
            //   },
            //   {
            //     offset: 1,
            //     color: '#F90583',
            //   },
            // ]),
            // shadowColor: 'rgba(32,0,159,0.3)',
            // shadowColor: 'rgba(140,0,72,0.1)',
            // shadowOffsetY: 12,
          },
          itemStyle: {
            color: '#008CFF',
            // color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            //   {
            //     offset: 0,
            //     color: '#6642F9',
            //   },
            //   {
            //     offset: 0.99,
            //     color: '#F90583',
            //   },
            // ]),
            // borderWidth: 6,
          },
          showSymbol: false,
          // areaStyle: {
          //   opacity: 0.1,
          //   color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          //     {
          //       offset: 0,
          //       color: '#5f45ff',
          //     },
          //     {
          //       offset: 0.99,
          //       color: '#f90583',
          //     },
          //   ]),
          // },
          emphasis: {
            focus: 'series',
          },
          data: dataChartState.valueData,
        },
      ],
    }),
    [dataChartState]
  );

  // const onNavTo = (val: string) => {
  //   if (isFunction(navigate)) {
  //     navigate(val);
  //   }
  // };

  useEffect(() => {
    setIsPermission(true);
    setShowLoading(false);
    setDataChartState({
      xData: ['1', '2', '3'],
      valueData: [300, 400, 600],
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <CardContext
      loading={showLoading}
      isPermission={isPermission}
      spinWrapperClassName={styles.spinWrap}
      appName={appName ?? t('desk_disCheck_newCount')}
    >
      <div className={styles.disNewChartCard}>
        <div className={styles.title}>
          <span className={styles.titleName}>{appName}</span>
        </div>
        {dataChartState.valueData.length > 0 && (
          <Echarts
            ref={echartsRef}
            id="psiDataChart"
            option={echartsOption}
            style={{ height: '220px', width: '100%' }}
          />
        )}

        {!dataChartState.valueData.length && (
          <div className={styles.empty}>{t('desk_disCheck_noData')}</div>
        )}
      </div>
    </CardContext>
  );
}

DisNewChartCard.defaultProps = {
  // appName: '新增分销商数量',
  appName: undefined,
};

export default DisNewChartCard;
