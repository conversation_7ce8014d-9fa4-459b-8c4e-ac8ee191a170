import React, { useEffect, useRef, useState } from 'react';
import { useRequest } from 'ahooks';
import { isFunction } from 'lodash';
import { Popover } from 'antd';
import classNames from 'classnames';
import { IEchBlockCard } from '../interface';
import useLang from '../utils/hooks/use-lang';
import NoPermissionCard from '../components/no-permission-card';
import useElSize from '../utils/hooks/use-el-size';
import getOrderPerm from './get-order-perm';
import zh from './locales/zh.json';
import en from './locales/en.json';
import './index.less';

const orders = [
  {
    src: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722585264533927.png`,
    title: 'desk_orderEntry_sales',
    url: '/indent/list?orderStatus=0&region=1&open=0',
    code: ['R_001_001', 'R_001_009', 'B_001_002_001'],
  },
  {
    src: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722585317408332.png`,
    title: 'desk_orderEntry_purchase',
    url: '/indent/list?orderStatus=0&region=0&open=0',
    code: ['R_001_002', 'R_001_010', 'B_001_003_001'],
  },
  {
    src: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722585356097798.png`,
    title: 'desk_orderEntry_production',
    url: '/indent/production',
    code: 'R_001_007',
  },
  {
    src: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/172258540908587.png`,
    title: 'desk_orderEntry_split',
    url: '/order/split/order/list',
    code: 'R_001_005',
  },
  {
    src: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722585447424581.png`,
    title: 'desk_orderEntry_purchaseReturn',
    url: '/indent/list?stockType=2&region=0',
    code: 'R_001_022',
  },
  {
    src: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722585497402647.png`,
    title: 'desk_orderEntry_salesReturn',
    url: '/indent/list?stockType=1&region=1',
    code: 'R_001_021',
  },
  {
    src: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722585591933715.png`,
    title: 'desk_orderEntry_sellToBuy',
    url: '/indent?isSellOnBuy=1',
    code: 'R_001_023',
  },
  {
    src: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/17225856311779.png`,
    title: 'desk_orderEntry_report',
    url: '/indent/report/buy?orderProperty=1',
    code: 'R_001_026',
  },
];

function OrderEntry({ navigate }: IEchBlockCard) {
  // 多语言导入
  const t = useLang({
    zh,
    en,
  });
  const [isPerm, setIsPerm] = useState(true);

  // 获取当前DOM元素
  const echBlockOrderEntryRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockOrderEntryRef, 150);

  const { run } = useRequest(getOrderPerm, {
    manual: true,
    onError: (err: any) => {
      if (err.code === 5) {
        setIsPerm(false);
      }
    },
  });

  const onNavTo = (val: string) => {
    // if (!checkCharge(code) || !checkPermission(code)) {
    //   message.warn('暂无权限');
    //   return;
    // }
    if (isFunction(navigate)) {
      navigate(val);
    }
  };

  useEffect(() => {
    run();
  }, [run]);

  return (
    <div
      ref={echBlockOrderEntryRef}
      className={classNames('ech-block-order-entry', {
        small: isSmall,
      })}
    >
      <div className="ech-block-order-entry-title">
        <img
          className="ech-block-order-entry-logo"
          src={`${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722585155099502.png`}
          alt=""
        />
        <span>{t('desk_orderEntry_title')}</span>
      </div>
      <div className="ech-block-order-entry-content">
        {orders.map((item) => (
          <div
            role="button"
            tabIndex={0}
            className="ech-block-order-entry-item"
            key={item.title}
            onClick={() => onNavTo(item.url)}
          >
            <img className="ech-block-order-entry-item-img" src={item.src} alt="" />

            <div className="ech-block-order-entry-item-text">
              <Popover content={t(item.title)}>
                <span
                  style={{
                    display: 'inline-block',
                    maxWidth: '100%',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}
                >
                  {t(item.title)}
                </span>
              </Popover>
            </div>
          </div>
        ))}
      </div>
      {!isPerm && (
        <NoPermissionCard
          appName={t('desk_orderEntry_management')}
          isSmall={isSmall}
          size={echBlockOrderEntryRef.current?.offsetWidth}
        />
      )}
    </div>
  );
}

export default OrderEntry;
