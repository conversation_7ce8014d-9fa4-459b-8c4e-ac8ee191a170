.financeReceivablePayable {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 12px;
  background-color: rgb(255 255 255 / 70%);
  backdrop-filter: blur(20px);
}

.headerRight {
  :global {
    .ant-radio-group {
      box-shadow: 0 1px 1px 0 rgb(0 0 0 / 8%);
      white-space: nowrap;
    }

    .ant-radio-group-solid
      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      color: #008cff;
      background: #d9eeff;
    }

    .ant-radio-group-small .ant-radio-button-wrapper {
      color: #888b98;
      font-size: 12px;
      padding: 0 10px;
    }
  }
}

.body {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

.bodyTop {
  color: #888b98;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  width: 100%;
  margin-bottom: 12px;
  justify-content: space-between;
  font-family: 'PingFang SC', sans-serif;
}

.bodyBottom {
  display: flex;
  width: 100%;
  height: 100%;
  padding: 5px 10px;
  justify-content: space-around;
  flex-direction: column;
  border-radius: 12px;
  background-color: #fff;
}

.rankItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.icon {
  width: 22px;
  height: 22px;
}

.rankItemTitle {
  color: #888b98;
  font-size: 13px;
  font-weight: normal;
  display: flex;
  min-width: 80px;
  line-height: normal;
  align-items: center;
  font-family: '苹方-简', sans-serif;

  img {
    margin-right: 3px;
  }
}

.rankItemPrice {
  flex: 1;
  text-align: right;
  color: #040919;
  font-size: 13px;
  font-weight: 400;
  line-height: normal;
  font-family: 'San Francisco Display', sans-serif;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 小尺寸样式
.small {
  &.financeReceivablePayable {
    padding: 10px;

    .headerRight {
      :global {
        .ant-radio-group {
          display: flex;
        }

        .ant-radio-group-small .ant-radio-button-wrapper {
          font-size: 6px;
          display: flex;
          height: 14px;
          padding: 0 8px;
          align-items: center;
        }
      }
    }

    .bodyTop {
      font-size: 8px;
      margin-bottom: 8px;
    }

    .icon {
      width: 12px;
      height: 12px;
    }

    .rankItemTitle {
      font-size: 8px;
      min-width: 68px;

      img {
        margin-right: 2px;
      }
    }

    .rankItemPrice {
      font-size: 8px;
    }
  }
}
