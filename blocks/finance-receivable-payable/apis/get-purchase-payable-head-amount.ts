import { createHttpRequest } from '@echronos/core';

export interface PurchasePayableHeadAmountParams {
  startDate: number; // 开始时间
  endDate: number; // 结束时间
  key?: string; // 公司名称
  pageSize?: number;
  pageNo?: number;
}

export interface PurchasePayableHeadAmountResponse {
  periodicDepositAmount: string;
  addAmount: string;
  takeBackAmount: string;
  endTermAmount: string;
}

/**
 * 采购应付-主页头部
 */
function getPurchasePayableHeadAmount(
  params: PurchasePayableHeadAmountParams
): Promise<PurchasePayableHeadAmountResponse> {
  return createHttpRequest('ech-order')('/v1/order/purchase/payableHeadAmount', {
    method: 'GET',
    params,
    autoToast: false,
  });
}

export default getPurchasePayableHeadAmount;
