import React, {
  forwardRef,
  HTMLAttributes,
  PropsWithChildren,
  useEffect,
  useImperativeHandle,
  useRef,
} from 'react';
import * as echarts from 'echarts';
import type { EChartsCoreOption, ECharts, init } from 'echarts';
import { isFunction } from 'lodash';
import appendScript from '../../utils/script';
import { EMPTY_FN } from '../../utils/const';

export interface EchartsProps extends PropsWithChildren<HTMLAttributes<HTMLDivElement>> {
  option?:
    | EChartsCoreOption
    | null
    | MultipleParamsFn<[echarts: typeof echarts], EChartsCoreOption | null>;
  initOpts?: Parameters<typeof init>[2];
  onInit?: MultipleParamsFn<[charts: ECharts]>;
}

export interface EchartsInstance {
  getInstance: () => Promise<ECharts>;
  setOption: MultipleParamsFn<[opt: EchartsProps['option'], notMerge?: boolean]>;
  getWrap: () => HTMLDivElement;
  getEcharts: () => ECharts | null;
}

function getEcharts() {
  let promise: Promise<typeof echarts>;
  if (import.meta.env.PROD) {
    promise = new Promise((resolve, reject) => {
      appendScript(
        'echarts',
        `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/vendor/echarts/echarts.custom.min.js`,
        resolve,
        reject
      );
    });
  } else {
    promise = import('echarts');
  }
  return promise;
}

const Echarts = forwardRef<EchartsInstance, EchartsProps>(
  ({ option, initOpts, onInit, ...props }, ref) => {
    const wrapEl = useRef(null as unknown as HTMLDivElement);
    const echartsRef = useRef<ECharts>();
    const getInstance = () => {
      if (echartsRef.current) {
        return Promise.resolve(echartsRef.current);
      }
      return getEcharts().then((charts) => {
        const instance = charts.init(wrapEl.current, initOpts);
        echartsRef.current = instance;
        if (onInit) {
          onInit(instance);
        }
        return Promise.resolve(instance);
      });
    };
    const setOption = (opt: EchartsProps['option'], notMerge = false) => {
      getInstance().then((instance) => {
        if (isFunction(opt)) {
          getEcharts().then((charts) => {
            const echartsOpt = opt(charts);
            if (echartsOpt) {
              console.log('echartsOpt', echartsOpt);

              instance.setOption(echartsOpt, notMerge);
            }
          });
        } else if (opt) {
          console.log('opt', opt);
          instance.setOption(opt, notMerge);
        }
      });
    };

    useEffect(
      () => () => {
        echartsRef.current?.dispose();
      },
      []
    );

    useEffect(() => {
      if (option) {
        setOption(option);
      }
    }, [option]); // eslint-disable-line

    useImperativeHandle(
      ref,
      () => ({
        getInstance,
        setOption,
        getWrap: () => wrapEl.current,
        getEcharts: () => echartsRef.current || null,
      }),
      // eslint-disable-next-line
      []
    );

    return <div {...props} ref={wrapEl} />;
  }
);

Echarts.defaultProps = {
  option: null,
  initOpts: undefined,
  onInit: EMPTY_FN,
};

export default Echarts;
