import { useTranslation } from 'react-i18next';
import React, {
  useState,
  useMemo,
  forwardRef,
  useImperativeHandle,
  memo,
  HTMLAttributes,
} from 'react';
import { useUpdateEffect } from 'ahooks';
import classNames from 'classnames';
import styles from './index.module.less';

export type CalendarYearProps = HTMLAttributes<HTMLDivElement> & {
  isShowHeadrSwitch?: boolean;
  isSmall?: boolean;
  currentYear?: number;
  // eslint-disable-next-line no-unused-vars
  onYearChange?: (year: number) => void;
};

export interface CalendarYearInstance {
  resetDate: () => void;
  prePageDate: () => void;
  nextPageDate: () => void;
}

const dateYearNum = new Date().getFullYear();
const CalendarYear = forwardRef<CalendarYearInstance, CalendarYearProps>(
  ({ isShowHeadrSwitch, isSmall, currentYear, onYearChange, ...props }, ref) => {
    const { t } = useTranslation();
    const calculateYear = currentYear || dateYearNum;
    const [selectYear, setSelectYear] = useState<number>(calculateYear || 0);
    const [newYearData, setNewYearData] = useState<number[]>([]);

    const getCurrentDefaultYearsMemo = useMemo(() => {
      const years: Array<number> = [];
      if (newYearData.length) {
        return newYearData;
      }
      if (isSmall) {
        for (let i = calculateYear - 2; i <= calculateYear + 6; i += 1) {
          years.push(i);
        }
        return years;
      }
      for (let i = calculateYear - 5; i <= calculateYear + 6; i += 1) {
        years.push(i);
      }
      return years;
    }, [isSmall, calculateYear, newYearData]);

    const modifyArrayBasedOnIndexListFn = (typeNum: 0 | 1 | -1) => {
      const { length } = getCurrentDefaultYearsMemo;
      let newYears: Array<number> = [];
      let adjustment = typeNum * length - 2;
      if (typeNum > 0) {
        newYears = getCurrentDefaultYearsMemo.map((x) => x + adjustment);
      }
      if (typeNum < 0) {
        adjustment = typeNum * length + 2;
        newYears = getCurrentDefaultYearsMemo.map((x) => x + adjustment);
      }
      setNewYearData(newYears);
    };

    useImperativeHandle(ref, () => ({
      resetDate: () => {
        setSelectYear(calculateYear);
        modifyArrayBasedOnIndexListFn(0);
      },
      prePageDate: () => {
        modifyArrayBasedOnIndexListFn(-1);
      },
      nextPageDate: () => {
        modifyArrayBasedOnIndexListFn(1);
      },
    }));

    useUpdateEffect(() => {
      if (onYearChange) {
        onYearChange(selectYear);
      }
    });

    return (
      <div
        className={classNames(styles.calendarYear, { [styles.small]: isSmall })}
        {...props}
        onWheel={(e) => {
          e.stopPropagation();
        }}
      >
        {isShowHeadrSwitch && (
          <div className={styles.calendarHeadr}>
            <div className={styles.calendarHeadrLeft}>
              <span
                className={styles.leftBtn}
                role="button"
                aria-label={t('desk_calendarYear_previous')}
                tabIndex={0}
                onClick={() => modifyArrayBasedOnIndexListFn(-1)}
              />
              <span className={styles.calendarHeadrText}>
                {getCurrentDefaultYearsMemo[0]} -{' '}
                {getCurrentDefaultYearsMemo[getCurrentDefaultYearsMemo.length - 1]}
              </span>
              <span
                className={styles.rightBtn}
                role="button"
                aria-label={t('desk_calendarYear_next')}
                tabIndex={0}
                onClick={() => modifyArrayBasedOnIndexListFn(1)}
              />
            </div>
            <span
              className={styles.calendarHeadrRightBtn}
              role="button"
              tabIndex={0}
              onClick={() => {
                setSelectYear(calculateYear);
                modifyArrayBasedOnIndexListFn(0);
              }}
            >
              {t('desk_calendarYear_reset')}
            </span>
          </div>
        )}
        <div className={styles.calendarBody}>
          {getCurrentDefaultYearsMemo?.map((year, index) => (
            <span
              key={year}
              className={classNames(styles.calendarText, {
                [styles.selcetDate]: year === selectYear,
              })}
              role="button"
              tabIndex={0}
              onClick={() => {
                setSelectYear(year);
                if (index === 0) {
                  modifyArrayBasedOnIndexListFn(-1);
                } else if (index === getCurrentDefaultYearsMemo.length - 1) {
                  modifyArrayBasedOnIndexListFn(1);
                }
              }}
            >
              {year}
            </span>
          ))}
        </div>
      </div>
    );
  }
);

CalendarYear.defaultProps = {
  isShowHeadrSwitch: true,
  isSmall: false,
  currentYear: 0,
  onYearChange: () => {},
};

export default memo(CalendarYear);
