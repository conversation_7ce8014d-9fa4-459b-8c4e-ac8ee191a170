.noDataCard {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.noDataCardContext {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.noData {
  color: #008cff;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.noDataImg {
  width: 32px;
  height: 26px;
  object-fit: cover;
}

.noDataMsg {
  color: #999eb2;
  font-size: 14px;
  min-width: 154px;
  margin-top: 12px;
}

// 小尺寸的卡片样式
._324 {
  .noData {
    font-size: 12px;
  }

  .noDataImg {
    width: 26px;
    height: 21px;
    object-fit: cover;
  }
}

._150 {
  .noData {
    font-size: 10px;
  }

  .noDataImg {
    width: 23px;
    height: 18px;
    object-fit: cover;
  }

  .noDataMsg {
    font-size: 8px;
    min-width: 88px;
    margin-top: 6px;
  }
}
