.noPermission {
  color: #fff;
  display: flex;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 99;
  flex-direction: column;
  background: rgb(0 0 0 / 40%);
  backdrop-filter: blur(25px);

  .noPermissionTile {
    padding: 12px;

    .noPermissionTileLogo {
      width: 22px;
      height: 22px;
      margin-right: 4px;
    }

    .noPermissionTileName {
      color: #fff;
      margin-right: 4px;
    }
  }

  .noPermissionContent {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .noPermissionContentDesc {
      margin-top: 6px;
    }
  }
}

// 小尺寸的卡片样式
._324 {
  .noPermissionTile,
  .noPermissionContent {
    font-size: 12px;
  }

  .noPermissionContent img {
    width: 17px;
    height: 18px;
  }
}

._150 {
  .noPermissionTile,
  .noPermissionContent {
    font-size: 8px;
  }

  .noPermissionContent img {
    width: 15px;
    height: 16px;
  }
}
