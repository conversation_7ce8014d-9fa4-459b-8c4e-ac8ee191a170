import React, { ReactNode } from 'react';
import classNames from 'classnames';
import useLang from '../../utils/hooks/use-lang';
import { IEchBlockCard } from '../../interface';
import zh from './locales/zh.json';
import en from './locales/en.json';
import styles from './index.module.less';

export type NoPermissionCardProps = IEchBlockCard & {
  appName?: string;
  cardName?: string;
  logo?: string;
  titleNode?: ReactNode;
  children?: ReactNode | null;
  isSmall?: boolean;
  size?: number;
};

function NoPermissionCard({
  appName,
  cardName,
  logo,
  titleNode,
  children,
  isSmall,
  size,
}: NoPermissionCardProps) {
  // 多语言导入
  const t = useLang({
    zh,
    en,
  });

  return (
    <div
      className={classNames(styles.noPermissionCard, {
        [styles[`_${size}`]]: isSmall,
      })}
    >
      <div className={styles.noPermission}>
        {titleNode || (
          <div className={styles.noPermissionTile}>
            {logo && <img alt="" src={logo} className={styles.noPermissionTileLogo} />}
            <span className={styles.noPermissionTileName}>{appName}</span>
          </div>
        )}

        <div className={styles.noPermissionContent}>
          <img
            src={`${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722659690912391.png`}
            alt=""
          />
          <div className={styles.noPermissionContentDesc}>{t('desk_noPermission_message')}</div>
          {cardName && <div className={styles.noPermissionCardName}>[{cardName}]</div>}
        </div>
      </div>
      {children}
    </div>
  );
}

NoPermissionCard.defaultProps = {
  appName: '',
  cardName: '',
  logo: '',
  titleNode: '',
  children: null,
  isSmall: false,
  size: 150,
};

export default NoPermissionCard;
