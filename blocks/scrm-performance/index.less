.ech-block-scrm-performance {
  color: #fff;
  display: flex;
  width: 100%;
  height: 100%;
  padding: 12px;
  overflow: hidden;
  position: relative;
  background-image: url('https://static.huahuabiz.com/static/img/1722671535354986.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  font-family: '苹方-简', sans-serif;
  flex-direction: column;
  cursor: pointer;

  .clip {
    display: block !important;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .right {
    text-align: right !important;
  }

  .headTitle {
    font-size: 16px;
    font-weight: 500;
    width: 100%;
    height: 26px;
    line-height: 26px;
  }

  .table {
    color: #fff;
    display: flex;
    margin-top: 6px;
    overflow: hidden;
    flex: 1;
    flex-direction: column;
    font-family: '苹方-简', sans-serif;
  }

  .tableHead {
    font-size: 14px;
    font-weight: 500;
    display: flex;
    padding: 8px 16px;
    border-radius: 13px;
    background: rgb(14 41 88 / 30%);
    backdrop-filter: blur(19px);
  }

  .maxWidth,
  .minWidth {
    font-size: 14px;
    font-weight: 500;
    display: flex;
    min-height: 20px;
    padding-right: 4px;
    justify-content: center;
    font-family: '苹方-简', sans-serif;
    align-items: center;
    text-align: center;
    word-break: break-all;
    word-wrap: break-word;
  }

  .placeLeft {
    justify-content: left;
  }

  .department {
    display: flex;
    align-items: center;

    .name {
      flex: 1;
      text-align: left;
    }
  }

  .placeRight {
    justify-content: right;
  }

  .content {
    min-width: 56px;
  }

  .maxWidth {
    width: 30%;
  }

  .minWidth {
    width: 20%;
  }

  .ant-spin-container {
    height: 100%;
  }

  .ant-spin-nested-loading {
    margin-top: 4px;
    overflow-y: auto;
    flex: 1;
    scrollbar-width: none;
  }

  .tableBody {
    height: 100%;
    padding: 0 14px;
  }

  .tableROw {
    display: flex;
    padding: 8px 0;
    border-bottom: 0.5px solid rgb(255 255 255 / 20%);
  }

  .editBtn {
    color: #040919;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    width: 100%;
    height: 40px;
    justify-content: center;
    position: absolute;
    bottom: 0;
    left: 0;
    align-items: center;
    background: linear-gradient(0deg, rgb(214 231 255 / 30%), rgb(214 231 255 / 30%)),
      rgb(255 255 255 / 20%);
    border-top: 1px solid rgb(255 255 255 / 30%);
    backdrop-filter: blur(30px);
    box-shadow: 0 -2px 5px 0 rgb(0 9 44 / 3%);
  }

  .nullData {
    display: flex;
    width: 100%;
    height: 100%;
    overflow: hidden;
    justify-content: center;
    align-items: center;
  }
}

.dis {
  background: none;
}

// 小尺寸的卡片样式
.small {
  &.ech-block-scrm-performance {
    .headTitle {
      font-size: 10px;
    }

    .tableHead {
      font-size: 8px;
      padding: 4px 16px;
    }

    .maxWidth,
    .minWidth {
      font-size: 8px;
    }

    .tableROw {
      padding: 4px 0;
    }

    .right_arrow_line {
      font-size: 12px !important;
    }

    .editBtn {
      font-size: 12px;
      height: 36px;
    }

    .nullData {
      font-size: 10px;
    }
  }
}
