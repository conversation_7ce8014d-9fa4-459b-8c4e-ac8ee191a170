import React, { useEffect, useRef, useState } from 'react';
import Icon from '@echronos/echos-icon';
import classNames from 'classnames';
import { Spin } from 'antd';
import useLang from '../utils/hooks/use-lang';
import useElSize from '../utils/hooks/use-el-size';
import SelectionCondition, { getCurrentQuarter } from '../components/selection-condition';
import { DeptRefundCardType, getDeptRefundCard, postQueryConditionSave } from './api';
import { IEchBlockCard } from '../interface';
import zh from './locales/zh.json';
import en from './locales/en.json';
import './index.less';

function PerformanceCard({ navigate, blockId }: IEchBlockCard) {
  const t = useLang({
    zh,
    en,
  });
  // 数据加载
  const [loading, setLoading] = useState(false);

  const [hover, setHover] = useState(false); // hover:编辑
  const [editBtnShow, setEditBtnShow] = useState(false); // 编辑按钮
  const [editBoxShow, setEditBoxShow] = useState(false); // 编辑盒子

  const [quarter, setQuarter] = useState<number[]>(getCurrentQuarter([])); // 选中的项
  // const [isActive, setIsActive] = useState<number | null>(null); // 高亮
  const [deptRefundData, setDeptRefundData] = useState<DeptRefundCardType>({
    deptRefundQuarterList: getCurrentQuarter([]),
    deptRefundList: [],
  });

  // 获取当前卡片的DOM元素
  const echBlockScrmPerformanceRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockScrmPerformanceRef, 324);

  const handleMouseEnter = () => {
    setHover(true);
    setEditBtnShow(true);
  };

  const handleMouseLeave = () => {
    setHover(false);
    setEditBtnShow(false);
  };

  //  回款列表
  const getReturnMoneyList = () => {
    setLoading(true);
    getDeptRefundCard(blockId?.toString() || '')
      .then((res) => {
        setDeptRefundData(res);
        setQuarter(res.deptRefundQuarterList);
        if (!res.deptRefundQuarterList.length) {
          postQueryConditionSave(getCurrentQuarter([]), blockId?.toString() || '').then(() => {
            getReturnMoneyList();
          });
        }
      })
      .finally(() => {
        setLoading(false);
        setEditBoxShow(false);
      });
  };

  // 完成
  const handleSuccess = (quarters: number[]) => {
    setLoading(true);
    postQueryConditionSave(quarters, blockId?.toString() || '').finally(() => getReturnMoneyList());
  };

  // 跳转进入数据分析页面
  const onJumpPage = () => {
    if (navigate) {
      navigate(`/scrm/client/report-forms?quarter=${deptRefundData.deptRefundQuarterList[0]}`);
    }
  };

  useEffect(() => {
    getReturnMoneyList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div
      ref={echBlockScrmPerformanceRef}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className={classNames('ech-block-scrm-performance', { dis: editBoxShow }, { small: isSmall })}
      role="button"
      tabIndex={0}
      onClick={onJumpPage}
    >
      {!editBoxShow ? (
        <>
          <div className="headTitle">
            {t('desk_scrmPerformance_title', {
              quarter: deptRefundData.deptRefundQuarterList.join('、'),
            })}
          </div>
          <div className="table">
            <div className="tableHead">
              <div className={classNames('maxWidth', 'placeLeft')}>
                {t('desk_scrmPerformance_department')}
              </div>
              <div className="minWidth">{t('desk_scrmPerformance_target')}</div>
              <div className="maxWidth clip">{t('desk_scrmPerformance_totalAmount')}</div>
              <div className={classNames('minWidth', 'placeRight', 'clip', 'right')}>
                {t('desk_scrmPerformance_expectedReturn')}
              </div>
            </div>
            <Spin spinning={loading} delay={500}>
              <div
                className="tableBody"
                onWheel={(e) => {
                  e.stopPropagation();
                }}
              >
                {deptRefundData.deptRefundList.length ? (
                  deptRefundData.deptRefundList.map((item) => (
                    <div className="tableROw" key={item.deptId}>
                      <div className={classNames('maxWidth', 'placeLeft')}>
                        <div className="department">
                          <span className="name">{item.deptName || '--'}</span>
                          <Icon name="right_arrow_line" size={16} />
                        </div>
                      </div>
                      <div className="minWidth">{item.targetMoney || '--'}</div>
                      <div className="maxWidth">{item.opportunityMoney || '--'} </div>
                      <div className={classNames('minWidth', 'placeRight')}>
                        <div className="content">{item.returnedMoney || '--'}</div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="nullData">{t('desk_scrmPerformance_noData')}</div>
                )}
              </div>
            </Spin>
            {/* 移入滚动的时候占位 */}
            {!loading && (editBtnShow || hover) ? <div style={{ height: '24px' }} /> : null}
          </div>

          {/* 编辑按钮 */}
          {editBtnShow && (
            <div
              className="editBtn"
              role="button"
              tabIndex={0}
              onClick={(e) => {
                e.stopPropagation();
                setEditBoxShow(true);
              }}
            >
              {t('desk_scrmPerformance_edit')}
            </div>
          )}
        </>
      ) : null}
      {!loading && (editBoxShow || hover) ? (
        <SelectionCondition
          size={echBlockScrmPerformanceRef.current?.offsetWidth}
          isSmall={isSmall}
          className="editBox"
          radioQuarter
          visible={editBoxShow}
          quartered={quarter}
          onSuccess={(quarters) => {
            handleSuccess(quarters);
          }}
        />
      ) : null}
    </div>
  );
}

export default PerformanceCard;
