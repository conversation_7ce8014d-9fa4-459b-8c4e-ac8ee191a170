import Icon from '@echronos/echos-icon';
import { Input, message } from 'antd';
import classNames from 'classnames';
import { useTranslation } from 'react-i18next';
import { ChangeEvent, useEffect, useMemo, useState } from 'react';
import { throttle } from 'lodash';
import { useMemoizedFn } from 'ahooks';
import getSelectCondition, { MemberListType } from './api';
import styles from './index.module.less';

const quarterList = [
  { label: 'desk_scrmCondition_q1', value: 1 },
  { label: 'desk_scrmCondition_q2', value: 2 },
  { label: 'desk_scrmCondition_q3', value: 3 },
  { label: 'desk_scrmCondition_q4', value: 4 },
];

interface propsType {
  tip?: string; // 描述信息
  memberName?: string; // 成员/团队  默认是成员
  className?: string; // 最外层盒子类名
  visible: boolean; // 是否显示
  quartered?: number[]; // 季度
  radioQuarter?: boolean; // 季度是否单选 true 单选
  memberType?: number; // 成员/部门
  radioMember?: boolean; // 成员是否单选 true 单选
  memberIded?: number[]; // 已选成员
  // eslint-disable-next-line no-unused-vars
  onSuccess: (quarter: number[], memberId?: number[]) => void;
}

// 判断当前第几季度
const getCurrentQuarter = (quarter: number[]) => {
  if (quarter.length) {
    return quarter;
  }
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth() + 1; // JavaScript中月份从0开始，所以要加1

  if (currentMonth >= 1 && currentMonth <= 3) {
    return [1]; // 1月到3月为第一季度
  }
  if (currentMonth >= 4 && currentMonth <= 6) {
    return [2]; // 4月到6月为第二季度
  }
  if (currentMonth >= 7 && currentMonth <= 9) {
    return [3]; // 7月到9月为第三季度
  }
  return [4]; // 10月到12月为第四季度
};

function SelectionCondition({
  visible,
  quartered,
  radioQuarter,
  memberType,
  radioMember,
  memberIded,
  className,
  tip,
  memberName,
  onSuccess,
}: propsType) {
  const { t } = useTranslation();
  const [quarter, setQuarter] = useState(getCurrentQuarter(quartered || [])); // 季度
  const [showquarter, setShowquarter] = useState(false); // 下拉选择季度
  const [showMember, setShowMember] = useState(false); // 是否选泽成员
  const [memberList, setMemberList] = useState<MemberListType[]>([]); // 成员列表
  const [inputValue, setInputValue] = useState(''); // 输入框的值
  const [filterList, setFilterList] = useState<MemberListType[]>([]); // 过滤的成员list
  const [memberIds, setMemberIds] = useState(memberIded);

  // 选中季度后的展示
  const isShowquarter = useMemo(() => {
    if (!quarter?.length) {
      return t('desk_scrmCondition_select');
    }
    return (
      quarterList
        .filter((item) => quarter?.includes(item.value))
        .map((item) => t(item.label))
        .join('、') || t('desk_scrmCondition_select')
    );
  }, [quarter]); // eslint-disable-line

  const isShowMember = useMemo(() => {
    if (!memberIds?.length) {
      return t('desk_scrmCondition_select');
    }
    return (
      memberList
        .filter((item) => memberIds?.includes(item.dataId))
        .map((item) => item.dataName)
        .join('、') || t('desk_scrmCondition_select')
    );
  }, [memberIds, memberList]); // eslint-disable-line

  // 选择季度
  const onSelectQuarte = (value: number) => {
    setMemberIds([]);
    if (radioQuarter) {
      setQuarter([value]);
      setShowquarter(false);
      return;
    }
    const newQuarter = quarter || [];
    const num = newQuarter.findIndex((item) => item === value);
    if (num === -1) {
      setQuarter([...newQuarter, value]);
    } else {
      setQuarter(newQuarter.filter((e) => e !== value));
    }
  };

  // 选择成员
  const onSelectMember = (value: number) => {
    if (radioMember) {
      setMemberIds([value]);
      setShowMember(false);
      setInputValue('');
      setFilterList(memberList);
      return;
    }
    const newMemberId = memberIds || [];
    const num = newMemberId.findIndex((i) => i === value);
    if (num === -1) {
      setMemberIds([...newMemberId, value]);
    } else {
      setMemberIds(newMemberId.filter((e) => e !== value));
    }
  };

  // 模糊查询;
  const filterMemberlist = throttle(
    useMemoizedFn((e: ChangeEvent<HTMLInputElement>) => {
      setInputValue(e.target.value);
      const list = memberList.filter((item) => item.dataName.includes(e.target.value));
      setFilterList(list);
    }),
    500
  );

  // 获取成员list
  useEffect(() => {
    if (visible && quarter && memberType) {
      const newQuarter = radioQuarter ? quarter[0] : quarter;
      getSelectCondition({ quarter: newQuarter, conditionType: memberType }).then((res) => {
        setMemberList(res.list);
        setFilterList(res.list);
      });
    }
  }, [memberType, quarter, radioQuarter, visible]);
  if (visible) {
    return (
      <div
        className={classNames(styles.editbox, className)}
        role="button"
        tabIndex={0}
        onClick={(e) => {
          e.stopPropagation();
          if (showquarter) {
            setShowquarter(false);
          }
          if (showMember) {
            setShowMember(false);
            setInputValue('');
            setFilterList(memberList);
          }
        }}
      >
        <div className={styles.editHead}>
          <div className={styles.logoName}>
            <img
              src={`${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722655712520460.png`}
              alt=""
            />
            {t('desk_scrmCondition_title')}
          </div>
          <div
            className={classNames(styles.success, 'scrm_onsuccess')}
            role="button"
            tabIndex={0}
            onClick={(e) => {
              e.stopPropagation();
              if (memberType) {
                if (!memberIds?.length) {
                  message.warn(`请确保选择了季度和${memberName}`);
                  return;
                }
                onSuccess(quarter as number[], memberIds);
              } else {
                onSuccess(quarter as number[]);
              }
            }}
          >
            {t('desk_scrmCondition_done')}
          </div>
        </div>
        <div className={classNames(styles.tip, 'scrm_tip')}>{tip}</div>
        <div className={styles.slectBox}>
          {!!quartered && (
            <div
              className={classNames(styles.select, 'scrm_labelName')}
              role="button"
              tabIndex={0}
              onClick={() => {
                if (showMember) return;
                setShowquarter(true);
              }}
            >
              <span className={styles.selectLabel}>{t('desk_scrmCondition_selectQuarter')}</span>
              <span className={styles.onselect}>{isShowquarter}</span>
            </div>
          )}
          {showquarter ? (
            <div className={styles.box}>
              {quarterList.map((item) => (
                <div
                  key={item.value}
                  className={classNames(styles.selectItem, {
                    [styles.selected]: quarter?.includes(item.value),
                  })}
                  role="button"
                  tabIndex={0}
                  onClick={() => {
                    onSelectQuarte(item.value);
                  }}
                >
                  {t(item.label)}
                </div>
              ))}
            </div>
          ) : null}
        </div>
        {!!memberType && (
          <div className={classNames(styles.slectBox, styles.tier)}>
            <div
              className={classNames(styles.select, 'scrm_labelName')}
              role="button"
              tabIndex={0}
              onClick={() => {
                if (showquarter) return;
                setShowMember(true);
              }}
            >
              <span className={styles.selectLabel}>
                {t('desk_scrmCondition_selectMember', { type: memberName })}
              </span>
              <span className={styles.onselect}>{isShowMember}</span>
            </div>
          </div>
        )}

        {showMember ? (
          <div
            className={classNames(styles.memberList, 'scrm_memberList')}
            role="button"
            tabIndex={0}
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <div className={styles.memberTitle}>{memberName}</div>
            <div className={styles.search}>
              <Input
                size="small"
                placeholder={t('desk_scrmCondition_search')}
                prefix={<Icon name="search_line" />}
                onChange={filterMemberlist}
                value={inputValue}
                allowClear
              />
              <span
                className={styles.cancel}
                role="button"
                tabIndex={0}
                onClick={() => {
                  setShowMember(false);
                  setInputValue('');
                  setFilterList(memberList);
                }}
              >
                {t('desk_scrmCondition_cancel')}
              </span>
            </div>
            <div
              className={styles.selectMember}
              onWheel={(e) => {
                e.stopPropagation();
              }}
            >
              {filterList.length ? (
                filterList.map((item) => (
                  <div
                    className={classNames(styles.itemMember, 'scrm_itemMember')}
                    role="button"
                    tabIndex={0}
                    onClick={() => {
                      onSelectMember(item.dataId);
                    }}
                    onWheel={(e) => {
                      e.stopPropagation();
                    }}
                  >
                    <span className={styles.member}>{item.dataName}</span>
                    <div className={styles.icon}>
                      {memberIds?.includes(item.dataId) && (
                        <Icon name="yes_line" size={16} color="#008CFF" />
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className={styles.nullData}>{t('desk_scrmCondition_noData')}</div>
              )}
            </div>
          </div>
        ) : null}
      </div>
    );
  }
  return null;
}

SelectionCondition.defaultProps = {
  quartered: null,
  radioQuarter: false,
  memberType: null,
  radioMember: false,
  memberIded: [],
  className: null,
  tip: '快速查看指定员工预计回款数据',
  memberName: '员工',
};

export default SelectionCondition;
