import React, { useState } from 'react';
import useLang from '../utils/hooks/use-lang';
import SelectionCondition from './conpoment';
import zh from './locales/zh.json';
import en from './locales/en.json';
import styles from './index.module.less';

function Scrm() {
  // 多语言导入
  const t = useLang({
    zh,
    en,
  });
  const [quartered, setQuartered] = useState<number[]>([]); // 季度
  const [visible, setVisible] = useState(false);

  return (
    <div className={styles.scrm_component}>
      <div
        role="button"
        tabIndex={0}
        onClick={() => {
          setVisible(true);
        }}
      >
        {t('desk_scrm_trigger')}
      </div>
      <SelectionCondition
        className={styles.pink}
        quartered={quartered}
        radioQuarter
        memberType={1}
        radioMember
        visible={visible}
        memberIded={[143]}
        onSuccess={(e) => {
          setQuartered(e);
          setVisible(false);
        }}
      />
    </div>
  );
}

export default Scrm;
