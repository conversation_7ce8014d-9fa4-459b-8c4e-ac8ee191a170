.scrm_component {
  color: #fff;
  display: flex;
  width: 100%;
  height: 100%;
  padding: 12px;
  overflow: hidden;
  position: relative;
  background-image: url('https://static.huahuabiz.com/static/img/1722671535354986.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  font-family: '苹方-简', sans-serif;
  flex-direction: column;
  cursor: pointer;
}

// .pink {
//   color: #fff;
//   background: linear-gradient(
//       345deg,
//       #000214 1%,
//       rgba(0, 6, 59, 0.84) 56%,
//       rgba(0, 6, 59, 0.45) 99%
//     ),
//     #0003a7;
//   :global {
//     // tip文字
//     .scrm_tip {
//       color: #888b98;
//     }

//     // 完成
//     .scrm_onsuccess {
//       color: #040919;
//       background: rgba(255, 255, 255, 0.22);
//       box-sizing: border-box;
//       border: 0.5px solid rgba(0, 0, 0, 0.1);
//       box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.08);
//     }

//     // 选择季度
//     .scrm_labelName {
//       color: #ffffff;
//       background: rgba(42, 46, 94, 0.8);
//     }

//     // 选人

//     .scrm_memberList {
//       color: #ffffff;
//       background: linear-gradient(0deg, rgba(57, 59, 156, 0.3), rgba(57, 59, 156, 0.3)), #1e1e1e;
//       border: 1px solid rgba(255, 255, 255, 0.05);
//     }

//     .scrm_itemMember {
//       border-bottom: 0.5px solid rgba(255, 255, 255, 0.2);
//     }
//   }
// }
