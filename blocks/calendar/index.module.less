.echBlockmCalendar {
  width: 100%;
  height: 100%;

  :global {
    .ant-picker-content {
      margin: 0 auto;
      transform: scale(0.95);
    }

    .ant-picker-calendar-mini {
      border-radius: 18px;
      overflow: hidden;
      background: rgb(255 255 255 / 70%);
      backdrop-filter: blur(25.6px);
      box-shadow: 0 4px 12px rgba(0 0 0 / 5%);
    }

    .ant-picker-calendar .ant-picker-panel {
      background: none;
      border-top: none;
    }

    .ant-picker-calendar .ant-picker-cell .ant-picker-cell-inner {
      width: 22px;
      min-width: 22px;
      height: 22px;
      line-height: 22px;
      border-radius: 50%;
    }

    .ant-picker-calendar
      .ant-picker-cell-in-view.ant-picker-cell-today
      .ant-picker-cell-inner::before {
      border: none;
    }

    .ant-picker-calendar-mini .ant-picker-content {
      height: 200px;
    }

    .ant-picker-calendar .ant-picker-cell {
      padding: 0;
    }
  }
}

.header {
  display: flex;
  padding: 12px 12px 0;
  justify-content: space-between;
  flex-direction: row;
}

.headerLeft {
  display: flex;
  align-items: center;
}

.calendarIcon {
  display: inline-block;
  width: 22px;
  height: 22px;
  background: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023719/1689737497636876.png');
  background-size: 100% 100%;
}

.title {
  color: #040919;
  font-size: 14px;
  font-weight: 600;
  margin-left: 4px;
  vertical-align: super;
}

.changetime {
  display: flex;
  align-items: center;
}

.changeIcon {
  font-size: 18px;
  vertical-align: sub;
}

// 小尺寸的卡片样式
.small {
  .calendarIcon {
    width: 12px;
    height: 12px;
  }

  .title {
    font-size: 8px;
    line-height: 11px;
    margin-left: 4px;
  }

  .changetime {
    font-size: 8px;
    margin-top: 2px;
  }

  .changeIcon {
    font-size: 10px;
    line-height: 11px;
  }

  :global {
    .ant-picker-calendar {
      width: 100%;

      .ant-picker-panel {
        width: 100%;

        .ant-picker-body {
          padding: 2px 0;

          .ant-picker-content {
            height: auto;

            th,
            td {
              font-size: 8px;
              width: 20px;
              height: 20px;
              line-height: 20px;

              .ant-picker-cell-inner {
                width: auto;
                height: 20px;
                line-height: 20px;
              }
            }
          }
        }
      }
    }
  }
}
