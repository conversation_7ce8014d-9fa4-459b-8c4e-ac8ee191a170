import { createHttpRequest, PaginationResponse } from '@echronos/core';

export type GetImsDisTenantListOption = {
  companyId: number;
  tenantId: string;
  tenantName: string;
};

/**
 * 获取分销站点
 * @param typeCode
 */
function getImsDisTenantList(): PaginationResponse<GetImsDisTenantListOption> {
  return createHttpRequest('ech-ims')('/drp/query/tenant/list', {});
}

export default getImsDisTenantList;
