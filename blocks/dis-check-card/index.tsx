import React, { useState, useRef, useEffect } from 'react';
import { isFunction } from 'lodash';
import { message, Popover } from 'antd';
import classNames from 'classnames';
import useLang from '../utils/hooks/use-lang';
import lang from '../utils/lang';
import { getImsDisTenantList, getImsDisStatistic } from './apis';
import type { GetImsDisTenantListOption, GetImsDisStatisticOption } from './apis';
import { IEchBlockCard } from '../interface';
import useElSize from '../utils/hooks/use-el-size';
import zh from './locales/zh.json';
import en from './locales/en.json';
import CardContext from '../components/card-context';
import styles from './index.module.less';

export type DisCheckCardProps = IEchBlockCard & {
  appName?: string;
  logo?: string;
};

function DisCheckCard({ appName, logo, navigate }: DisCheckCardProps) {
  // 多语言导入
  const t = useLang({
    zh,
    en,
  });
  const tenantList = useRef<GetImsDisTenantListOption[]>([]);
  const [isPermission, setIsPermission] = useState(true);
  const [showLoading, setShowLoading] = useState(false);
  const [disData, setDisData] = useState<GetImsDisStatisticOption>({
    normalCount: 0,
    pendingApprovalCount: 0,
    pendingApprovalNameList: [],
    tenantId: '',
  });
  // 获取当前卡片的DOM元素
  const disCheckCardRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(disCheckCardRef, 324);
  const onNavTo = (val: string) => {
    if (isFunction(navigate)) {
      navigate(val);
    }
  };

  const getCardData = () => {
    setShowLoading(true);
    const params = tenantList.current?.length ? { tenantId: tenantList.current[0].tenantId } : {};
    getImsDisStatistic(params)
      .then((res) => {
        setDisData(res);
      })
      .catch((err) => {
        if (err.code === 5) {
          setIsPermission(false);
        } else {
          message.error(err.message);
        }
      })
      .finally(() => {
        setShowLoading(false);
      });
  };

  const getDisTenantList = () => {
    getImsDisTenantList()
      .then((res) => {
        tenantList.current = res.list || [];
      })
      .finally(() => {
        getCardData();
      });
  };

  const toDisManage = () => {
    if (tenantList.current.length || disData.tenantId) {
      const tenantId = tenantList.current?.[0]?.tenantId || disData.tenantId || '';
      localStorage.setItem('dis_tenantId', tenantId);
      onNavTo(`/distributor/manage?tenantId=${tenantId}`);
    }
  };
  const toDisJoinAudit = () => {
    if (tenantList.current.length || disData.tenantId) {
      const tenantId = tenantList.current?.[0]?.tenantId || disData.tenantId || '';
      localStorage.setItem('dis_tenantId', tenantId);
      onNavTo(`/distribution/join-audit?tenantId=${tenantId}`);
    }
  };

  useEffect(() => {
    getDisTenantList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <CardContext
      loading={showLoading}
      isPermission={isPermission}
      spinWrapperClassName={styles.spinWrap}
      logo={logo}
      appName={appName}
      cardName={t('desk_disCheck_title')}
    >
      <div
        ref={disCheckCardRef}
        className={classNames(styles.disCheckCard, { [styles.small]: isSmall })}
      >
        <div className={styles.disCheckCardLeft}>
          <div className={styles.title}>
            <img alt="" src={logo} className={styles.titleLogo} />

            <span className={styles.titleName}>
              <Popover content={appName}>
                <span
                  style={{
                    display: 'inline-block',
                    maxWidth: '100%',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}
                >
                  {appName}
                </span>
              </Popover>
            </span>
          </div>
          <div className={styles.needCheck} role="presentation" onClick={() => toDisJoinAudit()}>
            <span className={styles.needCheckNum}>{disData.pendingApprovalCount}</span>
            <span>{t('desk_disCheck_unit')}</span>
          </div>
          <div className={styles.needCheckDesc}>{t('desk_disCheck_pendingReview')}</div>
          <div className={styles.normalBox} role="presentation" onClick={() => toDisManage()}>
            <div className={styles.greenDot} />
            <div className={styles.normalItem}>
              <span>{t('desk_disCheck_normal')}</span>
              <span className={styles.normalItemNum}>{disData.normalCount}</span>
            </div>
          </div>
        </div>
        <div className={styles.disCheckCardRight}>
          <div className={styles.disCheckCardRightTitle}>{t('desk_disCheck_pendingReview')}</div>
          {disData.pendingApprovalNameList?.map((item, index) =>
            index < 4 ? (
              <div
                className={styles.needCheckItem}
                role="presentation"
                onClick={() => toDisJoinAudit()}
                key={item}
              >
                <div className={styles.yellowDot} />
                <div className={styles.needCheckItemName}>{item}</div>
              </div>
            ) : null
          )}
          {!disData.pendingApprovalNameList.length && (
            <div className={styles.empty}>{t('desk_disCheck_noData')}</div>
          )}
        </div>
      </div>
    </CardContext>
  );
}

DisCheckCard.defaultProps = {
  appName: `${lang('desk_disCheck_defaultName', {
    zh,
    en,
  })}`,
  logo: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1697441685114984095.png`,
};

export default DisCheckCard;
