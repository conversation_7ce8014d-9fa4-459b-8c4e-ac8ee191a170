.echBlockWrap {
  width: 100%;
  height: 100%;
}

.disCheckCard {
  display: flex;
  height: 100%;
  border-radius: 18px;
  padding: 25px;
  // background-image: linear-gradient(62deg, #04c -1%, #22b086 102%),
  //   radial-gradient(64% 64% at 66% 37%, rgba(0 0 0 / 0%) 0%, rgba(0 0 0 / 60%) 100%);
  background-image: linear-gradient(105deg, #22b086 -1%, #04c 102%),
    radial-gradient(64% 64% at 66% 37%, rgba(0 0 0 / 0%) 0%, rgba(0 0 0 / 60%) 100%);
  background-color: rgba(0 0 0 / 30%);
  background-repeat: no-repeat;
  background-size: cover; /* 或者根据需要设置其他值 */

  .disCheckCardLeft {
    color: #fff;
    width: 208px;
    height: 208px;
    padding: 0 16px;
    border-radius: 14px;
    background: rgba(255 255 255 / 20%);
    backdrop-filter: blur(13px);
    box-shadow: 2px 4px 12px 0 rgba(0 0 0 / 8%), inset 0 0 1 0 #fff;

    .title {
      display: flex;
      margin-top: 16px;
      align-items: center;

      .titleLogo {
        width: 22px;
        height: 22px;
        margin-right: 4px;
      }

      .titleName {
        color: #fff;
      }
    }

    .needCheck {
      font-size: 14px;
      margin-top: 8px;
      cursor: pointer;

      .needCheckNum {
        font-size: 64px;
      }
    }

    .normalBox {
      display: flex;
      align-items: center;
      margin-top: 21px;
      cursor: pointer;

      .greenDot {
        width: 6px;
        height: 6px;
        background-color: #05d380;
        border-radius: 50%;
        margin-right: 4px;
      }

      .normalItemNum {
        margin-left: 8px;
      }
    }
  }

  .disCheckCardRight {
    color: #fff;
    flex: 1;
    margin-left: 32px;

    .disCheckCardRightTitle {
      font-size: 18px;
      margin-top: 10px;
      margin-bottom: 20px;
    }

    .needCheckItem {
      font-size: 14px;
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      cursor: pointer;

      .yellowDot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #f9ae08;
        margin-right: 10px;
      }
    }
  }
}

.spinWrap {
  height: 100%;

  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}

// 小尺寸的样式卡片
.small {
  padding: 20px;

  .disCheckCardLeft {
    width: 128px;
    height: 128px;
    padding: 0 12px;

    .title {
      margin-top: 10px;

      .titleLogo {
        width: 16px;
        height: 16px;
        margin-right: 3px;
      }

      .titleName {
        font-size: 10px;
        line-height: 11px;
      }
    }

    .needCheck {
      font-size: 8px;
      margin-top: 6px;
      cursor: pointer;

      .needCheckNum {
        font-size: 36px;
      }
    }

    .needCheckDesc {
      font-size: 8px;
      margin-top: 4px;
    }

    .normalBox {
      font-size: 8px;
      margin-top: 12px;

      .greenDot {
        width: 4px;
        height: 4px;
        margin-right: 3px;
      }

      .normalItemNum {
        margin-left: 8px;
      }
    }
  }

  .disCheckCardRight {
    margin-left: 20px;

    .disCheckCardRightTitle {
      font-size: 12px;
      margin-top: 8px;
      margin-bottom: 12px;
    }

    .needCheckItem {
      font-size: 10px;
      margin-bottom: 8px;

      .yellowDot {
        width: 5px;
        height: 5px;
        margin-right: 6px;
      }
    }
  }

  .empty {
    font-size: 10px;
  }
}
