import { RefObject, useEffect, useRef } from 'react';
import type { EchartsInstance } from '../../components/echarts/index';

const refs: RefObject<EchartsInstance>[] = [];

if (!import.meta.env.SSR) {
  window.addEventListener(
    'resize',
    () => {
      refs.forEach((ref) => {
        ref.current?.getEcharts()?.resize();
      });
    },
    false
  );
}

/**
 * Echarts hooks
 */
function useEcharts(): RefObject<EchartsInstance> {
  const echartsRef = useRef<EchartsInstance>(null);

  useEffect(() => {
    refs.push(echartsRef);
    return () => {
      const index = refs.indexOf(echartsRef);
      if (index !== -1) {
        refs.splice(index, 1);
      }
    };
  }, []);

  return echartsRef;
}

export default useEcharts;
