import { useRef } from 'react';

function replaceTemplate(str: string, opt?: Record<string, unknown>) {
  return str.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return opt?.[key] !== undefined ? String(opt[key]) : match;
  });
}

// 定义支持嵌套对象的多语言配置类型
type LangConfig = Record<string, string | Record<string, unknown>>;

function findValue(key: string, lang: LangConfig) {
  return lang[key];
}

function useLang(config: { zh: LangConfig; en: LangConfig }) {
  const lang = useRef<'zh' | 'en'>((localStorage.getItem('lang') || 'zh') as 'zh' | 'en');
  return (key: string, opt?: Record<string, unknown>) => {
    const keys = key.split('.');
    if (keys.length > 1) {
      let value = '';
      let findObj = config[lang.current][keys[0]];

      keys.forEach((item) => {
        if (typeof findObj === 'string') {
          value = findObj as string;
        } else {
          findObj = findValue(item, findObj as LangConfig);
        }
      });
      return value;
    }
    let value = config[lang.current][key] || key;
    if (opt && typeof value === 'string') {
      value = replaceTemplate(value, opt);
      return value;
    }

    if (typeof value === 'string') {
      return value;
    }
    return key;
  };
}

export default useLang;
