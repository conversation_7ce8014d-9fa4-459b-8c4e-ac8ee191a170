import { useSize } from 'ahooks';
import { RefObject, useMemo } from 'react';

function useElSize(el: HTMLDivElement | RefObject<HTMLDivElement> | null, smallCardWidth: number) {
  const elSize = useSize(el);

  const isSmallCard = useMemo<boolean>(
    () => elSize?.width === smallCardWidth,
    [elSize?.width, smallCardWidth]
  );

  if (!elSize) return false;
  return isSmallCard;
}

export default useElSize;
