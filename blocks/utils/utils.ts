import { isString } from 'lodash';
import dayjs from 'dayjs';
import { PRICE_FORMAT } from './const';
/**
 * 数字转金额千分位
 * @param price {string|number}
 * @param fractionDigits {number}
 */
// eslint-disable-next-line import/prefer-default-export
export function formatPrice(price: string | number, fractionDigits = 2): string {
  const value = isString(price) ? parseFloat(price) : price;
  if (Number.isNaN(value) || !value) {
    return (0).toFixed(fractionDigits);
  }
  const fractionPrice = value.toFixed(fractionDigits);
  if (fractionDigits > 3) {
    const index = fractionPrice.lastIndexOf('.');
    if (index !== -1) {
      return `${fractionPrice
        .substring(0, index)
        .replace(PRICE_FORMAT, ',')}${fractionPrice.substring(index)}`;
    }
  }
  return fractionPrice.replace(PRICE_FORMAT, ',');
}

/**
 * 格式化时间
 * @param time 时间戳
 * @param detail 是否显示时分
 * @returns 格式化后的时间 2023-10-11 or 2023-10-11 10:10
 * */
export const formatTime = (time: number, detail = false) => {
  if (detail) return dayjs(time).format('YYYY-MM-DD HH:mm');
  return dayjs(time).format('YYYY-MM-DD');
};
