import React, { PropsWithChildren } from 'react';
import './index.less';

export interface IBlockContainerProps {
  id: string; // 容器id
  verticalMargin?: number; // 水平边距
  horizontalMargin?: number; // 垂直边距
}

function BlockContainer({
  id,
  verticalMargin,
  horizontalMargin,
  children,
}: PropsWithChildren<IBlockContainerProps>) {
  return (
    <div
      id={id}
      className="ech-block-container"
      style={{
        width: `calc(100% - ${(verticalMargin || 0) * 2}px)`,
        height: `calc(100% - ${(horizontalMargin || 0) * 2}px)`,
      }}
    >
      {children}
    </div>
  );
}

BlockContainer.defaultProps = {
  verticalMargin: 12, // 水平边距
  horizontalMargin: 12, // 垂直边距
};

export default BlockContainer;
