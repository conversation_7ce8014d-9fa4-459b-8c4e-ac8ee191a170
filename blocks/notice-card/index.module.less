.echBlockNoticeCard {
  color: #fff;
  width: 100%;
  height: 100%;
  padding: 40px 16px 16px 20px;
  position: relative;
  font-family: '苹方-简', sans-serif;
  background-color: #fff;
  background-size: 100% 100% !important;
  background-position: center !important;
}

.echBlockNoticeCardTitle {
  font-size: 24px;
  font-weight: 500;
  line-height: 36px;
}

.echBlockNoticeCardContent {
  min-height: 156px;
  margin-top: 8px;
}

.echBlockNoticeCardArticleTitle {
  font-size: 18px;
  font-weight: 500;
  display: -webkit-box; /* 使用弹性盒模型 */
  min-height: 52px;
  line-height: 26px;
  overflow: hidden; /* 隐藏溢出部分 */
  text-shadow: 0 2px 6px rgb(0 0 0 / 70%);
  -webkit-box-orient: vertical; /* 垂直排列 */
  -webkit-line-clamp: 2; /* 限制为两行 */
  text-overflow: ellipsis; /* 显示省略号 */
}

.echBlockNoticeCardArticleContent {
  font-size: 14px;
  font-weight: normal;
  display: -webkit-box;
  min-height: 46px;
  line-height: 22px;
  margin-top: 10px;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  text-overflow: ellipsis;
  text-shadow: 0 2px 6px rgb(0 0 0 / 70%);
}

.echBlockNoticeCardMore {
  display: flex;
  justify-content: flex-end;

  .echBlockNoticeCardMoreContent {
    font-size: 14px;
    font-weight: 400;
    display: flex;
    line-height: 22px;
    align-items: center;
    margin-top: 26px;

    .echBlockNoticeCardMoreIcon {
      font-size: 18px !important;
      margin-left: 3px;
    }
  }
}

.echBlockNoticeCardNoDataTitle {
  color: #040919;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;

  .echBlockNewsCardNoDataLogo {
    margin-right: 5px;
  }
}

// 小尺寸的卡片样式
.small {
  padding: 26px 15px 12px 18px;

  .echBlockNoticeCardTitle {
    font-size: 18px;
    font-weight: 500;
    line-height: 18px;
  }

  .echBlockNoticeCardContent {
    min-height: 106px;
    margin-top: 8px;
  }

  .echBlockNoticeCardArticleTitle {
    font-size: 12px;
    min-height: 36px;
    line-height: 18px;
  }

  .echBlockNoticeCardArticleContent {
    font-size: 10px;
    min-height: 32px;
    line-height: 16px;
    margin-top: 4px;
  }

  .echBlockNoticeCardMore {
    .echBlockNoticeCardMoreContent {
      font-size: 10px;
      font-weight: 400;
      display: flex;
      line-height: 22px;
      align-items: center;
      margin-top: 12px;

      .echBlockNoticeCardMoreIcon {
        font-size: 14px !important;
        margin-left: 2px;
      }
    }
  }

  // 暂无数据
  .echBlockNoticeCardNoDataTitle {
    font-size: 8px;
    line-height: 12px;

    .echBlockNewsCardNoDataLogo {
      width: 12px;
      height: 12px;
      margin-right: 4px;
    }
  }
}

.noData {
  padding: 12px;
}

.noDataSmall {
  padding: 10px;
}
