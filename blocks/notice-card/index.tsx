import Icon from '@echronos/echos-icon';
import React, { useEffect, useRef, useState } from 'react';
import classNames from 'classnames';
import NoDataCard from '../components/no-data-card';
import useElSize from '../utils/hooks/use-el-size';
import useLang from '../utils/hooks/use-lang';
import { IEchBlockCard } from '../interface';
import styles from './index.module.less';
import getNoticeCardInfo, { NoticeListType } from './apis';
import zh from './locales/zh.json';
import en from './locales/en.json';

function NoticeCard({ navigate }: IEchBlockCard) {
  // 多语言导入
  const t = useLang({
    zh,
    en,
  });
  // 公告列表
  const [noticeList, setNoticeList] = useState<NoticeListType[]>([]);
  // 获取当前卡片的DOM元素
  const echBlockNoticeCardRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockNoticeCardRef, 150);
  // 获取卡片尺寸
  const [size, setSize] = useState<number>();

  // 跳转链接
  const handleNavigate = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (navigate) {
      navigate(`/announcement/detail/${noticeList[0].id}`);
    }
  };

  const toHome = () => {
    if (navigate) {
      navigate(`/announcement`);
    }
  };

  useEffect(() => {
    setSize(echBlockNoticeCardRef.current?.offsetWidth);
    getNoticeCardInfo({}).then((res) => {
      setNoticeList(res.list);
    });
  }, []);

  return (
    <div
      ref={echBlockNoticeCardRef}
      className={classNames(styles.echBlockNoticeCard, {
        [styles.small]: isSmall,
        [styles.noData]: noticeList.length === 0,
        [styles.noDataSmall]: isSmall && noticeList.length === 0,
      })}
      role="presentation"
      style={{
        backgroundImage: noticeList.length
          ? `url(${noticeList[0]?.cover} )`
          : `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1727332905646277.png`,
      }}
      onClick={toHome}
    >
      {noticeList.length ? (
        <>
          <div className={styles.echBlockNoticeCardTitle}>{t('desk_notice_title')}</div>

          <div className={styles.echBlockNoticeCardContent}>
            <div
              className={styles.echBlockNoticeCardArticleTitle}
              role="presentation"
              onClick={handleNavigate}
            >
              {noticeList[0]?.title}
            </div>
            <div
              className={styles.echBlockNoticeCardArticleContent}
              role="presentation"
              onClick={handleNavigate}
            >
              {noticeList[0]?.content}
            </div>
            <div
              className={styles.echBlockNoticeCardMore}
              role="presentation"
              onClick={handleNavigate}
            >
              <div className={styles.echBlockNoticeCardMoreContent}>
                {t('desk_notice_view')}
                <Icon name="right_arrow_line" className={styles.echBlockNoticeCardMoreIcon} />
              </div>
            </div>
          </div>
        </>
      ) : (
        <>
          <div className={styles.echBlockNoticeCardNoDataTitle}>
            <img
              src={`${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1727265734277306.png`}
              alt=""
              className={styles.echBlockNewsCardNoDataLogo}
            />
            {t('desk_notice_title')}
          </div>
          <NoDataCard isSmall={isSmall} size={size} />
        </>
      )}
    </div>
  );
}

export default NoticeCard;
