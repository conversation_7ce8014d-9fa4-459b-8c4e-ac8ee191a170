import React, { useEffect, useMemo, useRef, useState } from 'react';
import classNames from 'classnames';
import useElSize from '../utils/hooks/use-el-size';
import useLang from '../utils/hooks/use-lang';
import { IEchBlockCard } from '../interface';
import CardContext from '../components/card-context';
import zh from './locales/zh.json';
import en from './locales/en.json';
import getCustomerTotalCountCard from './api/get-customer-total-count';
import styles from './index.module.less';

function ScrmCustomersNumbers({ navigate, blockId }: IEchBlockCard) {
  // 多语言导入
  const t = useLang({
    zh,
    en,
  });

  const [loading, setLoading] = useState(false);
  const [customerNum, setCustomerNum] = useState(0);

  // 获取当前卡片的DOM元素
  const echBlockScrmOpportunityTotalAmountRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockScrmOpportunityTotalAmountRef, 150);

  const getTotalAmount = useMemo(() => `${customerNum}`, [customerNum]);

  useEffect(() => {
    if (blockId) {
      setLoading(true);
      getCustomerTotalCountCard(blockId)
        .then((res) => {
          setCustomerNum(res.customerTotalCount);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [blockId]);

  return (
    <CardContext
      size={echBlockScrmOpportunityTotalAmountRef.current?.offsetWidth}
      isSmall={isSmall}
      loading={loading}
      isPermission
      spinWrapperClassName={styles.spinWrapper}
    >
      <div
        ref={echBlockScrmOpportunityTotalAmountRef}
        role="button"
        tabIndex={0}
        onClick={(e) => {
          e.stopPropagation();
          if (navigate) {
            navigate('/scrm/client/firm-client');
          }
        }}
        className={classNames(styles.scrm_opportunity_total_amount, { [styles.small]: isSmall })}
      >
        <div
          className={classNames(styles.numbers, {
            [styles.smallNum]: getTotalAmount.length > 6,
          })}
          title={getTotalAmount.length > 8 ? getTotalAmount : undefined}
        >
          {getTotalAmount || '-'}
        </div>
        <div className={styles.labelName}>{t('desk_scrmCustomers_currentCustomers')}</div>
      </div>
    </CardContext>
  );
}

export default ScrmCustomersNumbers;
