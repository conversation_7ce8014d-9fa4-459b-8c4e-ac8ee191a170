.spinWrapper {
  width: 100%;
  height: 100%;
  background-image: url('https://static.huahuabiz.com/static/img/1724905796274851.png'),
    linear-gradient(180deg, #249ce7 100%, rgb(0 126 203 / 68%) 0%);
  background-position: 8px 16px, center;
  background-size: 260px 258px, cover;
  background-repeat: no-repeat, no-repeat;
}

.scrm_opportunity_total_amount {
  color: #fff;
  padding-top: 16px;
  padding-left: 100px;
}

.numbers {
  font-size: 42px;
  font-weight: 500;
  display: block;
  height: 58px;
  line-height: 58px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.smallNum {
  font-size: 32px;
}

.labelName {
  font-size: 18px;
  line-height: 1;
}

// 小尺寸样式
.small {
  &.scrm_opportunity_total_amount {
    padding-top: 14px;
    padding-left: 64px;

    .numbers {
      font-size: 24px;
      height: 30px;
      line-height: 24px;
    }

    .smallNum {
      font-size: 20px;
    }

    .labelName {
      font-size: 10px;
      line-height: 1;
    }
  }
}
