import { useTranslation } from 'react-i18next';
import React, { useEffect, useRef, useState } from 'react';
import classNames from 'classnames';
import NoDataCard from '../components/no-data-card';
import useElSize from '../utils/hooks/use-el-size';
import { IEchBlockCard } from '../interface';
import styles from './index.module.less';
import getSystemCardInfo, { SystemListType } from './apis';
import zh from './locales/zh.json';
import en from './locales/en.json';
import useLang from '../utils/hooks/use-lang';

function SystemCard({ navigate }: IEchBlockCard) {
  // 多语言导入
  const t = useLang({
    zh,
    en,
  });

  // 制度列表
  const [systemList, setSystemList] = useState<SystemListType[]>([]);
  // 获取当前卡片的DOM元素
  const echBlockSystemCardRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockSystemCardRef, 150);
  // 卡片尺寸
  const [size, setSize] = useState<number>();

  // 链接跳转
  const handleNavigate = (id: number) => {
    if (navigate) {
      navigate(`/institution/detail/${id}`);
    }
  };

  useEffect(() => {
    getSystemCardInfo({}).then((res) => {
      setSystemList(res.list);
      setSize(echBlockSystemCardRef.current?.offsetWidth);
    });
  }, []);

  return (
    <div
      ref={echBlockSystemCardRef}
      className={classNames(styles.echBlockSystemCard, { [styles.small]: isSmall })}
      role="presentation"
      onClick={() => {
        if (navigate) {
          navigate(`/institution`);
        }
      }}
    >
      <div className={styles.echBlockSystemCardHeader}>{t('desk_systemCard_title')}</div>
      <ul className={styles.echBlockSystemCardContent}>
        {systemList.length ? (
          systemList.slice(0, 5).map((item, index) => (
            <li
              className={styles.echBlockSystemCardItem}
              key={item.id}
              role="presentation"
              onClick={(e) => {
                e.stopPropagation();
                handleNavigate(item.id);
              }}
            >
              <span>{index + 1}</span>
              <p>{item.title}</p>
            </li>
          ))
        ) : (
          <NoDataCard isSmall={isSmall} size={size} />
        )}
      </ul>
    </div>
  );
}

export default SystemCard;
