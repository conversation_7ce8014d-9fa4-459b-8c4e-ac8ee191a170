import { createHttpRequest, PaginationResponse } from '@echronos/core';

/**
 * 获取制度卡片信息
 */

interface paramsType {
  categoryId?: number;
  searchKey?: string;
  pageNo?: number;
  pageSize?: number;
}

export interface SystemListType {
  id: number;
  title: string;
  content: string;
  cover: string;
}

function getSystemCardInfo(params: paramsType): PaginationResponse<SystemListType> {
  return createHttpRequest('ech-cms')('/v1/regulation/list/to/desk', {
    method: 'GET',
    params,
  });
}

export default getSystemCardInfo;
