import React, { useEffect, useRef, useState } from 'react';
import { Tooltip } from 'antd';
import classNames from 'classnames';
import useLang from '../utils/hooks/use-lang';
import { IEchBlockCard } from '../interface';
import useElSize from '../utils/hooks/use-el-size';
import SelectionCondition, { getCurrentQuarter } from '../components/selection-condition';
import styles from './index.module.less';
import CardContext from '../components/card-context';
import getDeptMatureCard, { DeptMatureCardType, DeptInfoType } from './api/get-dept-mature-card';
import postQueryConditionSave from './api/post-queryCondition-save';
import zh from './locales/zh.json';
import en from './locales/en.json';

function ScrmDeptMature({ navigate, blockId }: IEchBlockCard) {
  // 多语言导入
  const t = useLang({
    zh,
    en,
  });
  const [loading, setLoading] = useState(false);

  const [editBtnShow, setEditBtnShow] = useState(false); // 编辑按钮
  const [editBoxShow, setEditBoxShow] = useState(false); // 编辑盒子
  const [maskShow, setMaskShow] = useState(false); // 蒙版显隐

  const [quarter, setQuarter] = useState<number[]>(getCurrentQuarter([])); // 季度

  const [deptId, setDeptId] = useState<number[]>([]); // 团队id
  const [deptInfoList, setDeptInfoList] = useState<DeptInfoType[] | null>(null); // 团队信息列表

  const [deptRefundData, setDeptRefundData] = useState<DeptMatureCardType | null>(null); // 卡片数据

  // 获取当前卡片的DOM元素
  const echBlockScrmDeptMatureRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockScrmDeptMatureRef, 150);

  // tag的颜色
  const tagColorClass = (type: number, goalRate: number) => {
    // goalRate 占目标倍率
    // type 1-商机总金额 2-成熟商机总金额
    if (goalRate < 1) {
      return styles.tagRed;
    }
    if ((type === 1 && goalRate < 1.2) || (type === 2 && goalRate < 4)) {
      return styles.tagYellow;
    }
    return styles.tagGreen;
  };

  const handleMouseEnter = () => {
    if (maskShow) return;
    setEditBtnShow(true);
  };

  const handleMouseLeave = () => {
    if (maskShow) return;
    setEditBtnShow(false);
  };

  const getDeptMatureData = () => {
    getDeptMatureCard(blockId?.toString() || '')
      .then((res: DeptMatureCardType) => {
        if (!res.deptStatisticsQuarter.length || !res.deptStatisticsList.length) {
          setMaskShow(true);
          return;
        }
        // 保存当前查询条件
        setDeptRefundData(res);
        setDeptInfoList(res.deptList);
        setQuarter(res.deptStatisticsQuarter);
        setDeptId(res.deptStatisticsList);
        setEditBoxShow(false);
      })
      .finally(() => {
        setLoading(false);
        setEditBoxShow(false);
      });
  };

  // 完成
  const handleSuccess = (quarters: number[], deptIds: number[]) => {
    setLoading(true);
    postQueryConditionSave(quarters, deptIds, blockId?.toString() || '').finally(() =>
      getDeptMatureData()
    );
  };

  // 首次渲染时获取卡片数据
  useEffect(() => {
    getDeptMatureData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 跳转进入数据分析页面
  const onJumpPage = () => {
    if (maskShow) return;
    if (navigate) {
      navigate(`/scrm/client/report-forms?quarter=${deptRefundData?.deptStatisticsQuarter}`);
    }
  };

  return (
    <CardContext loading={loading} isPermission spinWrapperClassName={styles.spinWrap}>
      <div
        ref={echBlockScrmDeptMatureRef}
        className={classNames(styles.echBlockScrmDeptMature, { [styles.small]: isSmall })}
        role="button"
        tabIndex={0}
        onClick={onJumpPage}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {!editBoxShow ? (
          <>
            {/* 员工logo+员工名称 */}
            <div className={styles.headBox}>
              {deptInfoList && deptInfoList[0].deptName.length > 12 ? (
                <Tooltip title={deptInfoList[0]?.deptName}>
                  {deptInfoList[0]?.deptName || '--'}
                </Tooltip>
              ) : (
                <span className={styles.headName}>
                  {(deptInfoList && deptInfoList[0]?.deptName) || '--'}
                </span>
              )}
            </div>

            {/* 内容 */}
            <div className={styles.returnMoneyBox}>
              <div className={styles.totalAmount}>
                <div className={styles.money}>
                  {t('desk_scrmDeptMature_quarterOpportunity', { quarter: quarter[0] })}
                </div>
                <div
                  className={classNames(
                    styles.goalRate,
                    tagColorClass(2, Number(deptRefundData?.opportunityMoney.goalRate))
                  )}
                >
                  {deptRefundData?.opportunityMoney.goalRate}
                </div>
              </div>
              <div className={styles.matureTotalAmount}>
                <div className={styles.money}>{t('desk_scrmDeptMature_matureOpportunity')}</div>
                <div
                  className={classNames(
                    styles.goalRate,
                    tagColorClass(1, Number(deptRefundData?.matureOpportunityMoney.goalRate))
                  )}
                >
                  {deptRefundData?.matureOpportunityMoney.goalRate}
                </div>
              </div>
            </div>

            {/* 编辑按钮 */}
            {editBtnShow && (
              <div
                className={styles.editBtn}
                role="button"
                tabIndex={0}
                onClick={(e) => {
                  e.stopPropagation();
                  setEditBoxShow(true);
                }}
              >
                {t('desk_scrmDeptMature_edit')}
              </div>
            )}
          </>
        ) : (
          <SelectionCondition
            size={echBlockScrmDeptMatureRef.current?.offsetWidth}
            isSmall={isSmall}
            className={styles.editBox}
            memberName={t('desk_scrmDeptMature_team')}
            radioQuarter
            visible={editBoxShow}
            quartered={quarter}
            memberType={1}
            radioMember
            memberIded={deptId}
            onSuccess={(quarters, deptIds) => {
              handleSuccess(quarters, deptIds || []);
            }}
          />
        )}
        {/* 蒙版 */}
        {maskShow && (
          <div className={styles.mask}>
            <div className={styles.maskTitle}>{t('desk_scrmDeptMature_coverageRate')}</div>
            <div
              className={styles.maskBtn}
              role="button"
              tabIndex={0}
              onClick={(e) => {
                e.stopPropagation();
                setEditBoxShow(true);
                setMaskShow(false);
              }}
            >
              {t('desk_scrmDeptMature_setConditions')}
            </div>
          </div>
        )}
      </div>
    </CardContext>
  );
}

export default ScrmDeptMature;
