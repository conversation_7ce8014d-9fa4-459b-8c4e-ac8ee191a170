import React, { useEffect, useRef, useState } from 'react';
import { useRequest } from 'ahooks';
import Icon from '@echronos/echos-icon';
import { Progress } from 'antd';
import { isFunction } from 'lodash';
import classNames from 'classnames';
import useLang from '../utils/hooks/use-lang';
import { IEchBlockCard } from '../interface';
import NoPermissionCard from '../components/no-permission-card';
import useElSize from '../utils/hooks/use-el-size';
import getStockCardApi, { StockCardData } from './get-stock-card-api';
import zh from './locales/zh.json';
import en from './locales/en.json';
import './stock-card.less';

const stocks = [
  {
    type: 'enter',
    titleKey: 'desk_stockCard_enter',
    addUrl: '/stock/create?stockType=enter',
    totalUrl: '/stock/enter',
    approvalUrl: '/stock/enter?orderStatus=0',
    code: 'AV_001_002',
  },
  {
    type: 'out',
    titleKey: 'desk_stockCard_out',
    addUrl: '/stock/create?stockType=out',
    totalUrl: '/stock/out',
    approvalUrl: '/stock/out?orderStatus=0',
    code: 'AV_001_001',
  },
  {
    type: 'taking',
    titleKey: 'desk_stockCard_taking',
    addUrl: '/stock/create?stockType=taking',
    totalUrl: '/stock/taking',
    approvalUrl: '/stock/taking?orderStatus=0',
    code: 'AV_001_004',
  },
  {
    type: 'allot',
    titleKey: 'desk_stockCard_allot',
    addUrl: '/stock/create?stockType=allot',
    totalUrl: '/stock/allot',
    approvalUrl: '/stock/allot?orderStatus=0',
    code: 'AV_001_003',
  },
  {
    type: 'receive',
    titleKey: 'desk_stockCard_receive',
    addUrl: '/stock/create?stockType=receive',
    totalUrl: '/stock/receive',
    approvalUrl: '/stock/receive?orderStatus=0',
    code: 'AV_001_008',
  },
];

interface StockCardProps extends IEchBlockCard {
  type: string;
}

function StockCard({ type, navigate }: StockCardProps) {
  // 多语言导入
  const t = useLang({
    zh,
    en,
  });
  const [info, setInfo] = useState<StockCardData>({
    confirmedNum: 0,
    noConfirmedNum: 0,
    totalNum: 0,
  });
  const [isPerm, setIsPerm] = useState(true);
  const stockInfo = stocks.find((item) => item.type === type);

  // 获取当前DOM元素
  const echBlockStockCard = useRef<HTMLDivElement>(null);

  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockStockCard, 150);

  const { run } = useRequest(getStockCardApi, {
    manual: true,
    defaultParams: [{ type }],
    onSuccess: (res) => {
      setInfo(res);
    },
    onError: (err: any) => {
      if (err.code === 5) {
        setIsPerm(false);
      }
    },
  });

  const onNavTo = (val: string) => {
    if (isFunction(navigate)) {
      navigate(val);
    }
  };

  useEffect(() => {
    run({ type });
  }, [run, type]);

  return (
    <div ref={echBlockStockCard} className={classNames('ech-block-stock-card', { small: isSmall })}>
      <div className="ech-block-stock-card-progress">
        <Progress
          type="circle"
          percent={(info.confirmedNum / info.totalNum) * 100}
          strokeColor={{
            '0%': '#4ED1FF',
            '75%': '#2C7DFF',
            '100%': '#287BFF',
          }}
          width={86}
          strokeWidth={10}
          trailColor="#DFE5EE"
          showInfo={false}
        />
        <div
          role="button"
          tabIndex={0}
          className="ech-block-stock-card-progress-text"
          onClick={() => onNavTo(stockInfo?.totalUrl || '')}
        >
          <div className="ech-block-stock-card-progress-num">
            {info.totalNum}
            <span className="ech-block-stock-card-progress-num-unit">
              {t('desk_stockCard_unit')}
            </span>
          </div>
          <div className="ech-block-stock-card-progress-num-text">
            {t('desk_stockCard_total', { type: t(stockInfo?.titleKey || '') })}
          </div>
        </div>
      </div>
      <div className="ech-block-stock-card-info">
        <div
          role="button"
          tabIndex={0}
          className="ech-block-stock-card-add"
          onClick={() => onNavTo(stockInfo?.addUrl || '')}
        >
          <Icon className="ech-block-stock-card-add-icon" name="add_line" />
          <span>{t('desk_stockCard_create')}</span>
        </div>
        <div
          role="button"
          tabIndex={0}
          className="ech-block-stock-card-info-status"
          onClick={() => onNavTo(stockInfo?.approvalUrl || '')}
        >
          <div className="ech-block-stock-card-info-text">{info.noConfirmedNum}</div>
          <div className="ech-block-stock-card-info-tip">{t('desk_stockCard_pendingApproval')}</div>
        </div>
      </div>
      {!isPerm && (
        <NoPermissionCard
          appName={t('desk_stockCard_warehouseManagement')}
          isSmall={isSmall}
          size={echBlockStockCard.current?.offsetWidth}
        />
      )}
    </div>
  );
}

export default StockCard;
