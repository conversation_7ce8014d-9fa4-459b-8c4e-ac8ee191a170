import { createHttpRequest } from '@echronos/core';

/**
 * @description 入库单卡片
 */

export interface StockCardData {
  totalNum: number;
  confirmedNum: number;
  noConfirmedNum: number;
}

interface StockCardParams {
  type: string;
}

function getUrl(val: string) {
  switch (val) {
    case 'enter':
      return 'inOrderCard';
    case 'out':
      return 'outOrderCard';
    case 'taking':
      return 'inventoryOrderCard';
    case 'allot':
      return 'allotOrderCard';
    case 'receive':
      return 'receiveOrderCard';
    default:
      return '';
  }
}

function getStockCardApi(params: StockCardParams): Promise<StockCardData> {
  return createHttpRequest('ech-psi')(`/v1/platform/desk/card/${getUrl(params.type)}`, {
    autoToast: false,
  });
}

export default getStockCardApi;
