{"desk_stockApproval_enter": "Stock-in", "desk_stockApproval_out": "Stock-out", "desk_stockApproval_pendingTitle": "{{type}} Documents Pending Review", "desk_stockApproval_supplier": "Supplier/Purchasing Unit", "desk_stockApproval_orderType": "Document Type", "desk_stockApproval_noData": "No data", "desk_stockApproval_warehouseManagement": "Warehouse Management", "stock-order-enums": {"orderStatus": {"all": "All", "pending": "Pending", "approved": "Approved", "draft": "Draft", "oaApproving": "OA Approving"}, "otherOrderBusinessType": {"scrapOutbound": "Scrap Outbound", "internalWelfare": "Internal Welfare", "compensationOut": "Compensation Out", "other": "Other"}, "relateOrderType": {"purchaseOrder": "Purchase Order", "outsourcingOrder": "Outsourcing Order"}, "orderBusinessType": {"purchaseOrder": "Purchase Order", "outsourcingOrder": "Outsourcing Order"}, "inboundStatus": {"notInbound": "Not Inbound", "inbound": "Inbound", "partialInbound": "Partial Inbound"}, "status": {"draft": "Draft", "pending": "Pending", "rejected": "Rejected", "approved": "Approved", "acceptanceApproving": "Acceptance Approving"}, "statusStr": {"draft": "Draft", "pending": "Pending", "approved": "Approved", "shippingApproving": "Shipping Approving"}, "outboundStatusStr": {"notOutbound": "Not Outbound", "outbound": "Outbound", "partialOutbound": "Partial Outbound"}, "purchaseSubtypeName": {"purchaseOrder": "Purchase Order", "outsourcingPurchaseOrder": "Outsourcing Purchase Order"}, "returnGoodsSourceName": {"purchaseOrder": "Purchase Order", "outsourcingPurchaseOrder": "Outsourcing Purchase Order"}, "sourceName": {"giftInbound": "Gift Inbound", "compensationInbound": "Compensation Inbound", "otherInbound": "Other Inbound"}, "businessType": {"enter": {"otherInbound": "Other Inbound", "purchaseInbound": "Purchase Inbound", "produceInbound": "Produce Inbound", "salesReturn": "Sales Return", "allotInbound": "Allot Inbound", "paveGoodsInbound": "Pave Goods Inbound", "diskBalanceInbound": "Disk Balance Inbound"}, "out": {"otherOutbound": "Other Outbound", "salesOutbound": "Sales Outbound", "purchaseOutbound": "Purchase Outbound", "allotOutbound": "Allot Outbound", "paveGoodsOutbound": "Pave Goods Outbound", "diskBalanceOutbound": "Disk Balance Outbound"}, "default": {"otherOutbound": "Other Outbound", "otherInbound": "Other Inbound", "salesOutbound": "Sales Outbound", "purchaseInbound": "Purchase Inbound", "produceInbound": "Produce Inbound", "salesReturn": "Sales Return", "purchaseOutbound": "Purchase Outbound", "allotOutbound": "Allot Outbound", "allotInbound": "Allot Inbound"}}, "orderFuncType": {"onlineMall": "Online Mall", "mallSupplement": "Mall Supplement", "returnSupplement": "Return Supplement"}}}