import React, { useEffect, useRef, useState } from 'react';
import { isFunction } from 'lodash';
import { useRequest } from 'ahooks';
import classNames from 'classnames';
import { IEchBlockCard } from '../interface';
import { formatPrice } from '../utils/utils';
import CardContext from '../components/card-context';
import CardHeaderTitle from '../components/card-header-title';
import getFinanceHomeBalance from './apis/get-finance-home-balance';
import useElSize from '../utils/hooks/use-el-size';
import useLang from '../utils/hooks/use-lang';
import shangyouIcon from './images/shangyou-icon.png';
import xiayouIcon from './images/xiayou-icon.png';
import zh from './locales/zh.json';
import en from './locales/en.json';
import styles from './index.module.less';

export type FinanceUpstreamDownstreamProps = IEchBlockCard & {
  appName?: string;
  logo?: string;
};
function FinanceUpstreamDownstream({ appName, logo, navigate }: FinanceUpstreamDownstreamProps) {
  // 多语言导入
  const t = useLang({
    zh,
    en,
  });
  const [isPermission, setIsPermission] = useState(true);
  const [isHeader] = useState(false);

  // 获取当前卡片的DOM元素
  const echBlockFinanceUpstreamDownstreamRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockFinanceUpstreamDownstreamRef, 150);

  const { data, loading, run } = useRequest(
    () =>
      getFinanceHomeBalance()
        .then((res) => res.list)
        .catch((err) => {
          if (err.code === 5) {
            setIsPermission(false);
          }
          return [
            {
              upDown: 1,
              count: 0,
              totalBalance: 0,
            },
            {
              upDown: 2,
              count: 0,
              totalBalance: 0,
            },
          ];
        }),
    {
      manual: true,
    }
  );
  const onNavTo = (val: string) => {
    if (isFunction(navigate)) {
      navigate(val);
    }
  };

  const JumpFinanceFn = (type: 1 | 2) => {
    onNavTo(
      type
        ? `/finance-management/account-management/upstream-account`
        : `/finance-management/account-management/downstream-account`
    );
  };

  useEffect(() => {
    run();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <CardContext
      loading={loading}
      isPermission={isPermission}
      spinWrapperClassName={styles.spinWrap}
      logo={logo}
      appName={appName ?? t('desk_financeUD_defaultName')}
    >
      <div
        ref={echBlockFinanceUpstreamDownstreamRef}
        className={classNames(styles.financeUpstreamDownstream, { [styles.small]: isSmall })}
      >
        {isHeader && (
          <CardHeaderTitle logo={logo} cardName={appName ?? t('desk_financeUD_defaultName')} />
        )}
        <div className={styles.body}>
          <div className={styles.bodyLeft}>
            <div className={styles.titleTop}>{t('desk_financeUD_upDownstream')}</div>
            <div className={styles.titleButton}>{t('desk_financeUD_accountBalance')}</div>
          </div>
          <div className={styles.bodyRight}>
            {data?.map((item) => (
              <div
                className={styles.bodyRightItem}
                key={item.upDown}
                role="button"
                tabIndex={0}
                onClick={() => JumpFinanceFn(item.upDown as 1 | 2)}
              >
                <img
                  src={item.upDown === 1 ? shangyouIcon : xiayouIcon}
                  alt="icon"
                  style={{ width: '16px', height: '16px', marginRight: '6px' }}
                />
                <div
                  className={styles.bodyRightItemRight}
                  title={`¥${formatPrice(item.totalBalance || 0)}`}
                >
                  <div className={styles.price}>¥{formatPrice(item.totalBalance || 0)}</div>
                  <div className={styles.title}>
                    {item.upDown === 1
                      ? t('desk_financeUD_upstreamAccount', { count: item.count })
                      : t('desk_financeUD_downstreamAccount', { count: item.count })}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </CardContext>
  );
}

FinanceUpstreamDownstream.defaultProps = {
  // appName: '上下游账户',
  appName: undefined,
  logo: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/****************.png`,
};

export default FinanceUpstreamDownstream;
