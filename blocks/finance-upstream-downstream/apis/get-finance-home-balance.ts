import { createHttpRequest, PaginationResponse } from '@echronos/core';

export interface FinanceHomeBalance {
  upDown: 1 | 2; // 1上游,2下游
  count: number; // 统计数量
  totalBalance: number; // 统计余额
}
/**
 * 财务-部分统计-上下游余额统计
 */

function getFinanceHomeBalance(): PaginationResponse<FinanceHomeBalance> {
  return createHttpRequest('ech-finance')('/v1/finance/home/<USER>', {
    method: 'GET',
    autoToast: false,
  });
}

export default getFinanceHomeBalance;
