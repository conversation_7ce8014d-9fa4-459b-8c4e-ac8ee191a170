.purchaseNotice {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  background: #fff;
  box-shadow: 2px 4px 12px 0px rgba(2, 9, 58, 0.08);
  padding: 12px;
  display: flex;
  flex-direction: column;
}

.box {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.infoFirstLine {
  .number {
    font-size: 24px;
    color: #008cff;
    display: inline-block;
  }

  .companyName {
    font-size: 14px;
    color: #888b98;
    display: inline-block;
    vertical-align: 2px;
  }
}
.content {
  font-size: 14px;
  font-weight: 600;
  line-height: 24px;
  color: #040919;
}
