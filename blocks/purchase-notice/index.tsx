import React from 'react';
import Icon from '@echronos/echos-icon';
import CardHeaderTitle from '../components/card-header-title';
import useLang from '../utils/hooks/use-lang';
import zh from './locales/zh.json';
import en from './locales/en.json';
import styles from './index.module.less';

function Scrm() {
  // 多语言导入
  const t = useLang({
    zh,
    en,
  });

  return (
    <div className={styles.purchaseNotice}>
      <CardHeaderTitle
        logo="https://static.huahuabiz.com/static/img/1722998468679472.png"
        cardName={t('purchase_notice')}
        headerRight={
          <div>
            {t('more')} <Icon name="right_arrow_line" size={16} />
          </div>
        }
      />
      <div className={styles.box}>
        <div>
          <div className={styles.infoFirstLine}>
            <div className={styles.number}>01</div>
            <div className={styles.companyName}>佛山市顺博城市发展有限公司</div>
          </div>
          <div className={styles.content}>顺德预制菜产业发展基地场地围蔽材料采购项目（第三次）</div>
        </div>
        <div>
          <div className={styles.infoFirstLine}>
            <div className={styles.number}>02</div>
            <div className={styles.companyName}>佛山市顺博城市发展有限公司</div>
          </div>
          <div className={styles.content}>顺德预制菜产业发展基地场地围蔽材料采购项目（第三次）</div>
        </div>
      </div>
    </div>
  );
}

export default Scrm;
