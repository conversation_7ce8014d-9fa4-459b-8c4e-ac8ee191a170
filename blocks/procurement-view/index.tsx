/* eslint-disable no-use-before-define */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import classNames from 'classnames';
import { IEchBlockCard } from '../interface';
import useElSize from '../utils/hooks/use-el-size';
import useLang from '../utils/hooks/use-lang';
import style from './index.module.less';
import IndexModal from '../published-progress/indexModal';
// import logo from '../../assets/images/p.jpg';
import { briefingCard, editSave, getBriefing, getFlowInfoList, getQueryCondition } from './api';
import NoPermissionCard from '../components/no-permission-card';
import zh from './locales/zh.json';
import en from './locales/en.json';

interface flowInfoListType {
  name: string;
  value: string;
  checked: boolean;
}
interface procurementInfoType {
  number: string | number;
  title: string;
}
function ProcurementView({ blockId }: IEchBlockCard) {
  // 多语言导入
  const t = useLang({
    zh,
    en,
  });
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [flowInfoList, setFlowInfoList] = React.useState<Array<flowInfoListType>>([]);
  const [saveflowInfoList, setSaveFlowInfoList] = React.useState<Array<flowInfoListType>>([]);
  const [isPermission, setIsPermission] = React.useState(false);
  const [checkedData, setCheckedData] = React.useState({ name: '', value: 0 });
  const [procurementInfo, setProcurementInfo] = React.useState<Array<procurementInfoType>>([
    {
      number: '0',
      title: t('desk_procurementView_notIssued'),
    },
    {
      number: '0',
      title: t('desk_procurement_qualification'),
    },
    {
      number: '0',
      title: t('desk_procurement_bidding'),
    },
    {
      number: '0',
      title: t('desk_procurement_bidReturn'),
    },
    {
      number: '0',
      title: t('desk_procurement_evaluation'),
    },
    {
      number: '0',
      title: t('desk_procurement_negotiation'),
    },
    {
      number: '0',
      title: t('desk_procurement_contract'),
    },
    {
      number: '0',
      title: t('desk_procurement_all'),
    },
  ]);
  const [cancel, setCancel] = React.useState(false);
  const echBlockProcurementViewRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockProcurementViewRef, 150);

  const handleOk = () => {
    setIsModalOpen(false);
    editSave({ cardUuid: Number(blockId), flowInfoId: checkedData.value })
      .then(() => getQueryCondition({ cardUuid: blockId }))
      .then((res: { flowInfoId: number }) =>
        getBriefing({ flowInfoId: res.flowInfoId || checkedData.value })
      )
      .then(
        (res: {
          flowBriefingList: { count: number; flowCode: string; flowName: string }[];
          noIssueCount: number;
          totalCount: number;
        }) => {
          const data = res.flowBriefingList.map(
            (item: { count: number; flowCode: string; flowName: string }) => ({
              number: item.count,
              flowCode: item.flowCode,
              title: item.flowName,
            })
          );
          data.unshift({
            number: res.noIssueCount,
            flowCode: '-0',
            title: t('desk_procurementView_notIssued'),
          });
          data.push({
            number: res.totalCount,
            flowCode: '-1',
            title: t('desk_procurement_all'),
          });
          setProcurementInfo(data);
        }
      )
      .catch(() => {
        message.error(t('desk_procurement_editFailed'));
      });
  };

  const handleOpenModal = (e: { stopPropagation: () => void }) => {
    e.stopPropagation();
    setIsModalOpen(true);
  };
  const cancelSearch = () => {
    setCancel(false);
  };
  const checkStatus = (val: any) => {
    const data = saveflowInfoList.map((item: any) =>
      item.value === val.value
        ? { ...item, checked: true }
        : {
            ...item,
            checked: false,
          }
    );
    setFlowInfoList(data);
    setCancel(false);
    setSaveFlowInfoList(data);
    setCheckedData(val);
  };
  const getSearchResult = (e: { target: { value: string } }) => {
    // const t = flowInfoList.filter((item) => item.name.includes(e.target.value));
    // console.log(e.target.value)
    const t1 = saveflowInfoList.filter((item) =>
      item.name.toLowerCase().includes(e.target.value.toLowerCase())
    );
    if (e.target.value === '') {
      setFlowInfoList(saveflowInfoList);
    } else {
      setFlowInfoList(t1);
    }
  };
  // const changeSearchResult = (e) => {
  //   const t = flowInfoList.filter((item) =>
  //     item.name.toLowerCase().includes(e.target.value.toLowerCase())
  //   );
  //   setFlowInfoList(t);
  // };
  const selectPurchaseType = () => {
    setCancel(true);
    setFlowInfoList(saveflowInfoList);
  };
  useEffect(() => {
    briefingCard({})
      .then(() => {})
      .catch((err) => {
        if (err.code === 5) {
          setIsPermission(true);
        }
      });
    getQueryCondition({ cardUuid: blockId }).then((res: { flowInfoId: number }) => {
      getFlowInfoList()
        .then((rp) => {
          const data = rp.list.map(
            (item: { flowInfoName: string; flowInfoId: number }, index: number) => {
              if (res.flowInfoId && item.flowInfoId === res.flowInfoId) {
                const i = { name: item.flowInfoName, value: item.flowInfoId };
                setCheckedData(i);
                fn(i);
                return {
                  name: item.flowInfoName,
                  value: item.flowInfoId,
                  checked: true,
                };
              }
              if (!res.flowInfoId && index === 0) {
                const i = { name: item.flowInfoName, value: item.flowInfoId };
                setCheckedData(i);
                fn(i);
                return {
                  name: item.flowInfoName,
                  value: item.flowInfoId,
                  checked: true,
                };
              }

              return {
                name: item.flowInfoName,
                value: item.flowInfoId,
                checked: false,
              };
            }
          );

          setFlowInfoList(data);
          setSaveFlowInfoList(data);
        })
        .catch((err) => {
          if (err.code === 5) {
            setIsPermission(true);
          }
        });
    });
  }, []);
  const fn = (res: { value: number }) => {
    getBriefing({ flowInfoId: res.value }).then(
      (response: {
        flowBriefingList: { count: number; flowCode: string; flowName: string }[];
        noIssueCount: number;
        totalCount: number;
      }) => {
        const pageData = response.flowBriefingList.map(
          (item: { count: number; flowCode: string; flowName: string }) => ({
            number: item.count,
            flowCode: item.flowCode,
            title: item.flowName,
          })
        );
        pageData.unshift({
          number: response.noIssueCount,
          flowCode: '-0',
          title: t('desk_procurementView_notIssued'),
        });
        pageData.push({
          number: response.totalCount,
          flowCode: '-1',
          title: t('desk_procurement_all'),
        });
        setProcurementInfo(pageData);
      }
    );
  };
  return (
    <div
      ref={echBlockProcurementViewRef}
      className={classNames([style.outSide, { [style.small]: isSmall }])}
      id="procurementViewOutSide"
    >
      <div className={style.insideTop}>
        <img
          src={`${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722998468679472.png`}
          alt=""
          className={style.logo}
        />
        <span
          className={classNames(style.procurement, style.clip)}
          title={t('desk_procurement_title')}
        >
          {t('desk_procurement_title')}
        </span>
        <span className={style.demo}>
          {checkedData.name || t('desk_procurement_methodPlaceholder')}
        </span>
      </div>
      <div className={style.insideBottomWrap}>
        {procurementInfo.map((item) => (
          <div className={style.insideBottom} key={item.title}>
            <span className={style.number}>{item.number}</span>
            <span className={style.title}>{item.title}</span>
          </div>
        ))}
      </div>
      {!isPermission ? (
        <div className={style.editBtn} onClick={handleOpenModal} role="button" tabIndex={0}>
          {t('desk_progress_edit')}
        </div>
      ) : null}
      <IndexModal
        isModalOpen={isModalOpen}
        handleOk={handleOk}
        checkStatus={checkStatus}
        cancelSearch={cancelSearch}
        selectPurchaseType={selectPurchaseType}
        getSearchResult={getSearchResult}
        cancel={cancel}
        meun={flowInfoList}
        checkedValue={checkedData}
        size={echBlockProcurementViewRef.current?.offsetWidth}
        isSmall={isSmall}
      />
      {isPermission ? (
        <NoPermissionCard
          appName={t('desk_procurement_title')}
          isSmall={isSmall}
          size={echBlockProcurementViewRef.current?.offsetWidth}
        />
      ) : null}
    </div>
  );
}

export default ProcurementView;
