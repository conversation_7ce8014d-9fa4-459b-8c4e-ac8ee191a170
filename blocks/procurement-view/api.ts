import { createHttpRequest } from '@echronos/core';

function editSave(data: {
  cardUuid: number | undefined;
  flowInfoId?: number;
  statisticsFlowInfoId?: number;
}) {
  return createHttpRequest('ech-bidding')('/v1/home/<USER>/queryCondition/save', {
    method: 'POST',
    data,
  });
}

function getQueryCondition(params: { cardUuid: number | undefined }): Promise<any> {
  return createHttpRequest('ech-bidding')('/v1/home/<USER>/queryCondition/get', {
    method: 'GET',
    params,
  });
}

function getFlowInfoList(): Promise<any> {
  return createHttpRequest('ech-bidding')('/v1/bidding/config/flow/info/list', {
    method: 'GET',
  });
}
function getBriefing(params: { flowInfoId: number }): Promise<any> {
  return createHttpRequest('ech-bidding')('/v1/bidding/home/<USER>', {
    method: 'GET',
    params,
  });
}
function briefingCard(params: { flowInfoId?: number }): Promise<any> {
  return createHttpRequest('ech-bidding')('/v1/home/<USER>/briefingCard', {
    method: 'GET',
    params,
    autoToast: false,
  });
}

export { editSave, getQueryCondition, getFlowInfoList, getBriefing, briefingCard };
