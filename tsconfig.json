{"compilerOptions": {"experimentalDecorators": false, "target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "jsx": "react-jsx", "paths": {"@/*": ["./src/*"]}, "outDir": "./types", "declaration": true, "emitDeclarationOnly": true, "isolatedModules": false}, "include": ["blocks", "typings.d.ts"]}