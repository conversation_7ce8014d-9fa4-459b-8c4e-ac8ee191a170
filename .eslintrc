{"root": true, "extends": ["@echronos/eslint-config"], "rules": {"import/no-unresolved": ["error", {"ignore": ["antd"]}], "jsx-a11y/control-has-associated-label": "off", "jsx-a11y/no-static-element-interactions": "off", "no-console": "warn", "arrow-body-style": "off", "array-callback-return": "off", "consistent-return": "off", "no-unused-vars": "warn", "import/no-cycle": "warn", "eslint-disable react/require-default-props": "off", "no-underscore-dangle": "off", "camelcase": "off"}, "env": {"node": true, "browser": true}, "settings": {"import/resolver": {"node": {"paths": ["node_modules"], "extensions": [".js", ".jsx", ".ts", ".tsx"]}}}, "ignorePatterns": ["dist", "packages", "./jest.config.ts", "src", "*.cjs", "*.min.js", "micro.*.js", "micro.*.ts"]}