const { defineConfig } = require('rollup');
const fs = require('fs');
const path = require('path');
const nodeResolve = require('@rollup/plugin-node-resolve');
const commonjs = require('@rollup/plugin-commonjs');
const url = require('@rollup/plugin-url');
const json = require('@rollup/plugin-json');
const babel = require('@rollup/plugin-babel');
const alias = require('@rollup/plugin-alias');
const postCss = require('rollup-plugin-postcss');

const exts = ['.js', '.jsx', '.ts', '.tsx', '.cjs', '.mjs', '.json'];

const defaultPlugins = [
  nodeResolve({ extensions: exts }),
  commonjs(),
  json(),
  url(),

  babel({
    extensions: exts,
    babelHelpers: 'bundled',
  }),
  postCss({
    extract: true,
    extensions: ['.css', '.less'],
    minimize: true, // 可选配置，压缩生成的 CSS 文件
    use: {
      less: { javascriptEnabled: true },
    },
  }),
];

// 输出目录
const filePathMap = {};
// 排除的目录
const exclude = ['components', 'uoum-card-public', 'utils', 'interface.ts', 'size.ts', 'index.tsx'];
try {
  const files = fs.readdirSync(path.join(__dirname, './blocks'));

  files.forEach((file) => {
    if (!exclude.find((item) => item.includes(file))) {
      filePathMap[file] = `blocks/${file}/index.tsx`;
    }
  });
} catch (err) {
  console.error('Unable to scan directory: ' + err);
}
filePathMap['index'] = 'blocks/index.tsx';
console.log('filePathMap-----', filePathMap);

module.exports = defineConfig([
  {
    // input: 'src/lib/index.tsx',
    // output: {
    //   name: '@echronos/agent-chat',
    //   format: 'es',
    //   dir: 'packages/lib/dist',
    //   manualChunks() {
    //     return 'shared';
    //   },
    //   globals: {
    //     react: 'React', // 声明React为全局变量
    //   },
    // },
    input: filePathMap,
    output: {
      format: 'es',
      dir: './dist',
      entryFileNames: '[name]/index.js',
      preserveModules: true,
      preserveModulesRoot: 'blocks',
    },
    plugins: [...defaultPlugins],
    external: /^(?!.*swiper\/react)(react|dayjs|classnames|antd|lodash|ahooks|@echronos)/,
  },
]);
