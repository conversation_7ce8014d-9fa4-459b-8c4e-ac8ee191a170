{"compilerOptions": {"experimentalDecorators": false, "target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "jsx": "react-jsx", "baseUrl": "src", "paths": {"@/*": ["./src/*"], "antd": ["@echronos/antd"], "antd/*": ["@echronos/antd/*"]}, "declaration": true, "emitDeclarationOnly": true, "isolatedModules": false, "outDir": "./types"}, "include": ["block", "typings.d.ts"]}